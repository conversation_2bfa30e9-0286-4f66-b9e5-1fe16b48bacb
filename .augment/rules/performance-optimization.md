---
type: "agent_requested"
description: "Example description"
---

# Performance Optimization Rules

## Component Optimization Patterns

### Memoization Strategy
- **Use useMemo for expensive calculations** that depend on props/state
- **Use useCallback for event handlers** to prevent unnecessary re-renders
- **Extract helper functions** outside components for reusability and performance

### Example Patterns
```typescript
// Memoized expensive calculations
const processedData = useMemo(() => {
  return expensiveDataProcessing(rawData);
}, [rawData]);

// Memoized event handlers
const handleClick = useCallback((item: Item) => {
  // Handle click logic
}, [dependencies]);

// Extracted helper functions
const createOptimizedDataMap = (items: Item[]): Map<string, Item> => {
  return new Map(items.map(item => [item.id, item]));
};
```

## Data Processing Optimization

### Batch Operations
- **Group related updates** to minimize API calls
- **Use Map data structures** for O(1) lookups instead of array.find()
- **Implement early returns** in loops to avoid unnecessary processing

### Helper Function Pattern
```typescript
// Efficient batch processing
const createBatchUpdates = (items: Item[], updates: Update[]): Map<string, Item> => {
  const updateMap = new Map<string, Item>();
  const itemsMap = new Map(items.map(item => [item.id, item]));
  
  updates.forEach(update => {
    const item = itemsMap.get(update.itemId);
    if (item) {
      updateMap.set(update.itemId, { ...item, ...update.changes });
    }
  });
  
  return updateMap;
};
```

## Memory Management
- **Avoid creating objects in render loops**
- **Use stable references** for props and callbacks
- **Implement proper cleanup** in useEffect hooks
- **Minimize component re-renders** through proper dependency management
