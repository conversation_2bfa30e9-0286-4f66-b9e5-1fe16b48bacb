---
type: "agent_requested"
description: "Example description"
---

# State Management Rules

## Preferred State Management Solutions

### Primary Choice: Zustand Stores
- **Always prefer Zustand stores** over React contexts and Jotai atoms for state management
- Use Zustand for global application state, data stores, and cross-component communication
- Follow the existing pattern established in the codebase (useOrders, useProducts, useTables, etc.)

### Pattern Examples
```typescript
// Preferred Zustand store pattern
export const useOrders = create<OrderStore>((set) => ({
  orders: [],
  deleteOrder: () => {},
  createOrder: () => {},
  updateOrder: () => {},
  initialize: () => {
    // YJS integration logic
  },
}));
```

### Avoid
- React Context API for complex state management
- Jotai atoms for global state
- Redux or other heavy state management libraries

## YJS Integration
- **Keep YService.ts files untouched** during refactoring
- Maintain existing YJS CRDT integration patterns
- Use the established store initialization pattern with YJS document observation
