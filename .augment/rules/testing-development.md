---
type: "agent_requested"
description: "Example description"
---

# Testing and Development Rules

## Testing Philosophy

### Unit Testing Policy
- **Do not add or update unit tests** unless explicitly mentioned by the user
- Focus on functional implementation over test coverage
- Prioritize working features over comprehensive testing

### Manual Testing Approach
- **Suggest manual testing** of implemented features
- **Recommend browser testing** for UI components
- **Verify functionality** through user interaction rather than automated tests

## Development Workflow

### Package Management
- **Always use package managers** for dependency management
- **Never manually edit** package.json, requirements.txt, or similar files
- **Use appropriate package manager commands** for each technology:
  - JavaScript/Node.js: `npm install`, `yarn add`, `pnpm add`
  - Python: `pip install`, `poetry add`
  - Rust: `cargo add`

### Code Quality
- **Maintain TypeScript strict mode** compliance
- **Fix compilation errors** before considering features complete
- **Use proper type definitions** and avoid `any` types
- **Follow existing code patterns** and conventions

## Error Handling

### Build Process
- **Address TypeScript errors** that affect new functionality
- **Ignore existing legacy errors** unless they impact current work
- **Ensure new code compiles** without introducing additional errors

### Runtime Considerations
- **Implement proper error boundaries** for React components
- **Handle edge cases** in data processing
- **Provide fallback UI states** for loading and error conditions
