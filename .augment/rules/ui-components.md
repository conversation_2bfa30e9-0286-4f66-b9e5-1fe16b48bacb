---
type: "agent_requested"
description: "Example description"
---

# UI Components and Libraries Rules

## Form Components
### Preferred: Native PrimeReact Forms
- **Always use native PrimeReact forms** over Formik
- Leverage PrimeReact's built-in form components and validation
- Follow PrimeReact patterns for form handling and state management

### Example Pattern
```typescript
// Preferred PrimeReact form approach
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";

// Use native PrimeReact components with controlled state
```

## Data Tables
### Preferred: AG-Grid React for Admin Sections
- **Use AG-Grid React** over TanStack Table for admin sections
- Follow the established pattern in admin order and product pages

### Example Pattern
```typescript
// Preferred AG-Grid pattern
import { AgGridReact } from "ag-grid-react";
import type { ColDef } from "ag-grid-community";

const columnDefs: ColDef<DataType>[] = [
  // Column definitions
];
```

## Theme and Styling
### Light Theme Preference
- **Prefer light theme** over dark theme for admin UI layouts
- Maintain dark mode support but default to light theme
- Use consistent color schemes across admin interfaces

### Mobile-Optimized Design
- **Prefer compact, mobile-optimized** table/button lists
- Use smaller heights for quick navigation
- Avoid unnecessary legends or bottom sections
- Focus on efficient space utilization
