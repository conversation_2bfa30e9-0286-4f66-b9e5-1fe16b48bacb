import { PublicUser } from '@kassierer/shared/auth';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Request,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { AuthGuard } from 'src/auth/auth.guard';
import { AdminGuard } from 'src/auth/admin.guard';
import { JwtPayload } from 'src/auth/auth.model';
import { AuthService } from 'src/auth/auth.service';
import { User } from 'src/users/user.entity';
import { UsersService } from 'src/users/users.service';

type SignInDto = {
  email: string;
  password: string;
};

type SignUpDto = {
  email: string;
  password: string;
};

type ClientLoginDto = {
  token: string;
};

@Controller('auth')
export class AuthController {
  constructor(
    private authService: AuthService,
    private userService: UsersService,
  ) {}

  // Always a user, not a client
  @HttpCode(HttpStatus.CREATED)
  @Post('signup')
  async signUp(@Body() dto: SignUpDto) {
    if (!dto.email || !dto.password) {
      throw new BadRequestException();
    }
    const user = await this.userService.createOne(dto.email, dto.password);
    const publicUser: Omit<
      User,
      'password' | 'isActive' | 'clients' | 'yDocumentName' | 'document'
    > = {
      email: user.email,
    };
    return publicUser;
  }

  // Always a user, not a client
  @HttpCode(HttpStatus.OK)
  @Post('login')
  signIn(@Body() dto: SignInDto) {
    if (!dto?.email || !dto?.password) {
      throw new BadRequestException();
    }
    return this.authService.signIn(dto.email, dto.password);
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  profile(@Request() req): PublicUser {
    const { sub, isUserClient } = req.user as JwtPayload;

    if (isUserClient) {
      return {
        id: sub,
      };
    }

    return {
      id: sub,
      email: sub,
    };
  }

  @UseGuards(AdminGuard)
  @Post('client')
  async createClient(@Request() req) {
    const { sub } = req.user as JwtPayload;

    // create and return
    const { id } = await this.userService.createClientForUser(sub);
    // sign a jwt
    return this.authService.signClient(id);
  }

  @UseGuards(AdminGuard)
  @Post('temporary-token')
  async generateTemporaryToken(@Request() req) {
    const { sub } = req.user as JwtPayload;

    // Generate temporary token for the user
    const temporaryToken = await this.userService.generateTemporaryToken(sub);

    return {
      token: temporaryToken.id,
      expiresAt: temporaryToken.expiresAt,
    };
  }

  @HttpCode(HttpStatus.OK)
  @Post('client-login')
  async clientLogin(@Body() dto: ClientLoginDto) {
    if (!dto?.token) {
      throw new BadRequestException('Token is required');
    }

    // Validate the temporary token
    const temporaryToken = await this.userService.validateTemporaryToken(dto.token);
    if (!temporaryToken) {
      throw new UnauthorizedException('Invalid or expired token');
    }

    // Mark token as used
    const tokenUsed = await this.userService.useTemporaryToken(dto.token);
    if (!tokenUsed) {
      throw new UnauthorizedException('Token could not be used');
    }

    // Create a new client for the user
    const client = await this.userService.createClientForUser(temporaryToken.userId);
    if (!client) {
      throw new BadRequestException('Failed to create client');
    }

    // Generate and return JWT for the client
    return this.authService.signClient(client.id);
  }

  @UseGuards(AdminGuard)
  @Get('clients')
  async getClients(@Request() req) {
    const { sub } = req.user as JwtPayload;

    // Get all clients for the user
    const clients = await this.userService.getClientsForUser(sub);
    return clients;
  }

  @UseGuards(AdminGuard)
  @Delete('clients/:id')
  async deactivateClient(@Request() req, @Param('id') clientId: string) {
    const { sub } = req.user as JwtPayload;

    // Deactivate the client (only if it belongs to the user)
    await this.userService.deactivateClient(clientId, sub);
    return { success: true };
  }
}
