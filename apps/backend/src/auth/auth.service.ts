import { Injectable, UnauthorizedException } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { SignInResponseDto } from '@kassierer/shared/auth/signInResponse';
import { JwtPayload } from 'src/auth/auth.model';

@Injectable()
export class AuthService {
  constructor(
    private userService: UsersService,
    private jwtService: JwtService,
  ) {}

  async signClient(id: string) {
    const jwtPayload: JwtPayload = {
      sub: id,
      isUserClient: true,
    };
    return {
      access_token: await this.jwtService.signAsync(jwtPayload, {
        expiresIn: '7d', // Client tokens expire after 1 week
      }),
    };
  }

  async signIn(
    email: string,
    loginPassword: string,
  ): Promise<SignInResponseDto> {
    const user = await this.userService.findOne(email);

    if (!user) {
      throw new UnauthorizedException();
    }

    const isPasswordMatching = await bcrypt.compare(
      loginPassword,
      user.password,
    );

    if (isPasswordMatching === false) {
      throw new UnauthorizedException();
    }

    const jwtPayload: JwtPayload = {
      sub: user.email,
      isUserClient: false,
    };
    return {
      access_token: await this.jwtService.signAsync(jwtPayload),
    };
  }
}
