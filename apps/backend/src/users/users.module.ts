import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from 'src/users/user.entity';
import { UserClient } from 'src/users/userClient.entity';
import { TemporaryToken } from 'src/users/temporaryToken.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, UserClient, TemporaryToken])],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
