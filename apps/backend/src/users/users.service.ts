import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from 'src/users/user.entity';
import { Repository, LessThan } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { UserClient } from 'src/users/userClient.entity';
import { TemporaryToken } from 'src/users/temporaryToken.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(UserClient)
    private clientRepo: Repository<UserClient>,
    @InjectRepository(TemporaryToken)
    private temporaryTokenRepo: Repository<TemporaryToken>,
  ) {}

  async findUserForClient(clientId: string) {
    const { user } = await this.clientRepo.findOneByOrFail({
      id: clientId,
    });
    return this.findOne(user);
  }

  async findOne(email: string): Promise<User | undefined> {
    return this.userRepo.findOneBy({
      email: email,
    });
  }

  async createOne(email: string, password: string): Promise<User> {
    const salt = await bcrypt.genSalt();
    const hash = await bcrypt.hash(password, salt);

    return this.userRepo.save({
      email: email,
      yDocumentName: email,
      password: hash,
      isActive: true,
    });
  }

  async createClientForUser(user: string) {
    try {
      return this.clientRepo.save({
        isActive: true,
        user: user,
      });
    } catch (e) {
      console.error(e);
    }
  }

  // Temporary token methods
  async generateTemporaryToken(userId: string): Promise<TemporaryToken> {
    // Generate a 6-character alphanumeric token
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let token = '';
    for (let i = 0; i < 6; i++) {
      token += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    // Set expiration to 5 minutes from now
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5);

    // Clean up expired tokens before creating new one
    await this.cleanupExpiredTokens();

    // Check if token already exists (very unlikely but possible)
    const existingToken = await this.temporaryTokenRepo.findOneBy({ id: token });
    if (existingToken) {
      // Recursively try again with a new token
      return this.generateTemporaryToken(userId);
    }

    return this.temporaryTokenRepo.save({
      id: token,
      userId,
      expiresAt,
      isUsed: false,
    });
  }

  async validateTemporaryToken(token: string): Promise<TemporaryToken | null> {
    const temporaryToken = await this.temporaryTokenRepo.findOneBy({
      id: token,
    });

    if (!temporaryToken) {
      return null;
    }

    // Check if token is expired
    if (temporaryToken.expiresAt < new Date()) {
      return null;
    }

    // Check if token is already used
    if (temporaryToken.isUsed) {
      return null;
    }

    return temporaryToken;
  }

  async useTemporaryToken(token: string): Promise<boolean> {
    const temporaryToken = await this.validateTemporaryToken(token);

    if (!temporaryToken) {
      return false;
    }

    // Mark token as used
    await this.temporaryTokenRepo.update(
      { id: token },
      { isUsed: true }
    );

    return true;
  }

  private async cleanupExpiredTokens(): Promise<void> {
    // Remove tokens that expired more than 1 hour ago
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);

    await this.temporaryTokenRepo.delete({
      expiresAt: LessThan(oneHourAgo),
    });
  }

  // Client management methods
  async getClientsForUser(userId: string): Promise<UserClient[]> {
    return this.clientRepo.find({
      where: { user: userId },
      order: { id: 'DESC' },
    });
  }

  async deactivateClient(clientId: string, userId: string): Promise<void> {
    const client = await this.clientRepo.findOneBy({ id: clientId });

    if (!client) {
      throw new NotFoundException('Client not found');
    }

    if (client.user !== userId) {
      throw new ForbiddenException('You can only deactivate your own clients');
    }

    await this.clientRepo.update({ id: clientId }, { isActive: false });
  }
}
