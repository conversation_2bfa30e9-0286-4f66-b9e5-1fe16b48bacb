/* eslint-disable @typescript-eslint/no-empty-function */
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { setupWSConnection, setPersistence } from 'y-websocket/bin/utils';
import { Server } from 'ws';
import { JwtService } from '@nestjs/jwt';
import { authConstants } from 'src/auth/constants';
import { UsersService } from 'src/users/users.service';
import { JwtPayload } from 'src/auth/auth.model';
import { LeveldbPersistence } from 'y-leveldb';
import { UnauthorizedException } from '@nestjs/common';
import { applyUpdate, Doc, encodeStateAsUpdate } from 'yjs';
import { InjectRepository } from '@nestjs/typeorm';
import { UserDocument } from 'src/y-websocket/UserDocument.entity';
import { Repository } from 'typeorm';

@WebSocketGateway({
  cors: { origin: '*' },
  path: 'yjs',
})
export class YjsGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  constructor(
    private jwtService: JwtService,
    private userService: UsersService,
    @InjectRepository(UserDocument)
    private documentRepo: Repository<UserDocument>,
  ) {
    this.bindState = this.bindState.bind(this);
    this.writeState = this.writeState.bind(this);
  }

  @WebSocketServer()
  server: Server;

  afterInit(server: Server) {
    server.on('error', console.error);
  }

  async bindState(id: string, document: Doc) {
    // initialize the document with persisted content
    console.log('Binding state', id);

    const persisted = await this.documentRepo.findOneBy({
      documentName: id,
    });

    if (persisted?.content) {
      applyUpdate(document, persisted.content);
    } else {
      console.log('Document not found', id);
    }
  }

  async writeState(id: string, document: Doc) {
    // store the Yjs document to your database
    // this is called when all clients disconnect
    console.log('writing state', id);
    const content = Buffer.from(encodeStateAsUpdate(document));

    this.documentRepo.upsert(
      {
        documentName: id,
        content: content,
        user: 'j',
      },
      ['documentName'],
    );
  }

  connectY(yDocumentName: string, connection: WebSocket, request: any) {
    setupWSConnection(connection, request, { docName: yDocumentName });
    // const persistence = new LeveldbPersistence('./y-storage');
    setPersistence({
      bindState: this.bindState,
      writeState: this.writeState,
    });
  }

  async handleConnection(connection: WebSocket, request): Promise<void> {
    // We can handle authentication of user like below

    // const token = request.headers.get('auth_token');
    const requestUrl = new URL('https://localhost' + request.url);
    const token = requestUrl.searchParams.get('auth_token');

    const ERROR_CODE_WEBSOCKET_AUTH_FAILED = 4000;
    if (!token) {
      connection.close(ERROR_CODE_WEBSOCKET_AUTH_FAILED);
      return;
    }

    let payload: JwtPayload | undefined = undefined;

    try {
      payload = this.jwtService.verify<JwtPayload>(token, {
        secret: authConstants.jwtSecret,
      });
    } catch (e) {
      console.error('error with jwt token in ws connect');
      connection.close(ERROR_CODE_WEBSOCKET_AUTH_FAILED);
      return;
    }

    if (!payload) {
      connection.close(ERROR_CODE_WEBSOCKET_AUTH_FAILED);
      return;
    }

    const { sub, isUserClient } = payload;

    if (isUserClient) {
      const { yDocumentName } = await this.userService.findUserForClient(sub);
      if (!yDocumentName) {
        throw new UnauthorizedException();
      }
      this.connectY(yDocumentName, connection, request);
      return;
    }

    // connect a regular user
    const user = await this.userService.findOne(sub);
    if (!user) {
      throw new UnauthorizedException();
    }

    const { yDocumentName } = user;
    if (!yDocumentName) {
      throw new UnauthorizedException();
    }
    this.connectY(yDocumentName, connection, request);
  }

  handleDisconnect(): void {}
}
