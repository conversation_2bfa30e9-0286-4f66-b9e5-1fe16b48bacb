import { useUserStore } from "@/stores/userStore";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function RootPage() {
  const navigate = useNavigate();
  const isClient = useUserStore((state) => !state.user?.email);
  const token = useUserStore((state) => state.token);
  const isAuthPending = useUserStore((state) => state.isAuthPending);

  useEffect(() => {
    if (!isAuthPending) {
      if (!token) {
        navigate("/login");
      // } else if (isClient) {
        // navigate("/kassierer");
      } else {
        navigate("/admin/products");
      }
    }
  }, [token, isClient, isAuthPending, navigate]);

  if (isAuthPending) {
    return (
      <div className="flex min-h-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Laden...</p>
        </div>
      </div>
    );
  }

  return <div>Weiterleitung...</div>;
}
