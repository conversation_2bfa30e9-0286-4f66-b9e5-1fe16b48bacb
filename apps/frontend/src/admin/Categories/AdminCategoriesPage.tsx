import { useState, useCallback, useMemo } from "react";
import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import { But<PERSON> } from "primereact/button";
import type {
  ColDef,
  ICellRendererParams,
  CellValueChangedEvent,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { ProductCategory } from "@kassierer/shared/model";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

ModuleRegistry.registerModules([AllCommunityModule]);

// Temporary category type for local state
type TempCategory = {
  id: string;
  name: string;
  isTemporary: true;
};

// Combined type for table data
type TableCategory = (ProductCategory & { isTemporary?: false }) | TempCategory;

// Update type for temporary categories
type TempCategoryUpdate = Partial<Omit<TempCategory, "id" | "isTemporary">>;

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
  onDeleteTemp,
}: {
  data: TableCategory;
  onDelete: (category: ProductCategory) => void;
  onDeleteTemp: (id: string) => void;
}) => {
  const isTemporary = "isTemporary" in data && data.isTemporary;

  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => {
          if (isTemporary) {
            onDeleteTemp(data.id);
          } else {
            onDelete(data as ProductCategory);
          }
        }}
      />
    </div>
  );
};

function AdminProductCategories() {
  const categories = useProductCategories((state) => state.categories);
  const deleteCategory = useProductCategories((state) => state.deleteCategory);
  const createCategory = useProductCategories((state) => state.createCategory);
  const updateCategory = useProductCategories((state) => state.updateCategory);
  const products = useProducts((state) => state.products);

  // Local state for temporary categories
  const [tempCategories, setTempCategories] = useState<TempCategory[]>([]);

  // Combine real and temporary categories for display
  const allCategories: TableCategory[] = [...tempCategories, ...categories];

  // Calculate product counts per category
  const productCounts = useMemo(() => {
    const counts: Record<string, number> = {};
    products.forEach((product) => {
      if (product.categoryId) {
        counts[product.categoryId] = (counts[product.categoryId] || 0) + 1;
      }
    });
    return counts;
  }, [products]);

  // Helper functions
  const addTempCategory = useCallback(() => {
    const newTempCategory: TempCategory = {
      id: crypto.randomUUID(),
      name: "",
      isTemporary: true,
    };
    setTempCategories((prev) => [newTempCategory, ...prev]);
  }, []);

  const updateTempCategory = useCallback(
    (id: string, updates: TempCategoryUpdate) => {
      setTempCategories((prev) =>
        prev.map((category) =>
          category.id === id ? { ...category, ...updates } : category,
        ),
      );
    },
    [],
  );

  const saveTempCategory = useCallback(
    (tempCategory: TempCategory) => {
      if (tempCategory.name) {
        createCategory({
          name: tempCategory.name,
        });
        setTempCategories((prev) => prev.filter((c) => c.id !== tempCategory.id));
      }
    },
    [createCategory],
  );

  const deleteTempCategory = useCallback((id: string) => {
    setTempCategories((prev) => prev.filter((c) => c.id !== id));
  }, []);

  // Handle cell value changes
  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<TableCategory>) => {
      const { data, colDef, newValue } = event;
      const isTemporary = "isTemporary" in data && data.isTemporary;

      if (!isTemporary) {
        const category = categories.find((c) => c.id === data.id);
        if (category) {
          updateCategory(data.id, {
            ...category,
            [colDef.field!]: newValue,
          });
        }
        return;
      }

      if (isTemporary) {
        const field = colDef.field;
        const updates: TempCategoryUpdate = {};

        if (field === "name") {
          updates.name = newValue;
        }

        updateTempCategory(data.id, updates);

        // Check if category should be saved
        const updatedCategory = tempCategories.find((c) => c.id === data.id);
        if (updatedCategory) {
          const finalCategory = { ...updatedCategory, ...updates };
          if (finalCategory.name) {
            saveTempCategory(finalCategory);
          }
        }
      }
    },
    [updateTempCategory, tempCategories, saveTempCategory, categories, updateCategory],
  );

  const columnDefs: ColDef<TableCategory>[] = [
    {
      headerName: "Status",
      field: "isTemporary",
      width: 100,
      valueFormatter: (params) => {
        const isTemporary = params?.data?.isTemporary;
        return isTemporary ? "Neu" : "OK";
      },
      cellEditor: "agTextCellEditor",
      sortable: false,
      filter: false,
      editable: false,
    },
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      editable: true,
      sortable: false,
      filter: true,
    },
    {
      headerName: "Anzahl Produkte",
      width: 150,
      valueGetter: (params) => {
        return productCounts[params.data!.id] || 0;
      },
      sortable: true,
      filter: "agNumberColumnFilter",
      editable: false,
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableCategory>) => (
        <ActionCellRenderer
          data={params.data!}
          onDelete={({ id }) => deleteCategory(id)}
          onDeleteTemp={deleteTempCategory}
        />
      ),
      sortable: false,
      filter: false,
      editable: false,
    },
  ];

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        {/* Content goes here */}
        {/* We use less vertical padding on card headers on desktop than on body sections */}

        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Produkt Kategorien</h1>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              className="block rounded-md cursor-pointer bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              onClick={addTempCategory}
            >
              Erstellen
            </button>
          </div>
        </div>
      </div>
      <div>
        <div
          className="ag-theme-quartz"
        >
          <AgGridReact
            rowData={allCategories}
            columnDefs={columnDefs}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true,
            }}
            pagination={true}
            paginationPageSize={10}
            domLayout="autoHeight"
            getRowId={(params) => params.data.id}
            onCellValueChanged={onCellValueChanged}
          />
        </div>
      </div>
    </div>
  );
}

export default AdminProductCategories;
