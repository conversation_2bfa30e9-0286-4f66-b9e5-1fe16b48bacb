import { useTables } from "@/data/useTables";
import { useOrders } from "@/data/useOrders";
import { useProducts } from "@/data/useProducts";
import type { ColDef, RowClickedEvent } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Order, Product } from "@kassierer/shared/model";
import { PriceCentUtils } from "@/utils/priceCents";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import { useMemo, useState } from "react";

ModuleRegistry.registerModules([AllCommunityModule]);

function AdminOrderPage() {
  const tables = useTables(state => state.tables);
  const products = useProducts(state => state.products);
  const orders = useOrders((state) => state.orders);

  // State for selected order
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  const productsMap = useMemo(() => {
    return products.reduce<Record<string, Product>>((pMap, product) => {
      pMap[product.id] = product;
      return pMap;
    }, {});
  }, [products]);

  // Helper function to calculate order total
  const calculateOrderTotal = (order: Order): number => {
    return order.content.reduce((total, contentItem) => {
      const product = productsMap[contentItem.productId];
      if (product) {
        return total + (product.priceCents * contentItem.items.length);
      }
      return total;
    }, 0);
  };

  // Helper function to get table name
  const getTableName = (tableId: string): string => {
    const table = tables.find(t => t.id === tableId);
    return table?.name || '-';
  };

  // Helper function to count total products in order
  const getTotalProductCount = (order: Order): number => {
    return order.content.reduce((total, contentItem) => {
      return total + contentItem.items.length;
    }, 0);
  };

  // Handle row click
  const onRowClicked = (event: RowClickedEvent<Order>) => {
    setSelectedOrder(event.data!);
  };

  const columnDefs: ColDef<Order>[] = [
    {
      headerName: "Datum & Zeit",
      field: "createdAt",
      width: 200,
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleString('de-DE', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      },
      sortable: true,
      initialSort: "desc",
      filter: true,
      editable: false,
    },
    {
      headerName: "Tisch",
      field: "tableId",
      width: 150,
      valueFormatter: (params) => {
        return getTableName(params.value);
      },
      sortable: true,
      filter: true,
      editable: false,
    },
    {
      headerName: "Anzahl Produkte",
      width: 180,
      valueGetter: (params) => {
        return getTotalProductCount(params.data!);
      },
      sortable: true,
      filter: "agNumberColumnFilter",
      editable: false,
    },
    {
      headerName: "Gesamtsumme",
      width: 150,
      valueGetter: (params) => {
        return calculateOrderTotal(params.data!);
      },
      valueFormatter: (params) => {
        return PriceCentUtils.toMoneyString(params.value);
      },
      sortable: true,
      filter: "agNumberColumnFilter",
      editable: false,
    },
  ];

  // Order detail component
  const OrderDetail = ({ order }: { order: Order }) => {
    const table = tables.find(t => t.id === order.tableId);

    // Group items by product and note (similar to the kassierer order list)
    const groupedItems = useMemo(() => {
      const groups: Array<{
        product: Product;
        items: Array<{ note?: string; payed: boolean }>;
        note?: string;
        key: string;
      }> = [];

      order.content.forEach((orderContentItem) => {
        const product = productsMap[orderContentItem.productId];
        if (!product) return;

        // Group items by note
        const itemsByNote = new Map<string, Array<{ note?: string; payed: boolean }>>();

        orderContentItem.items.forEach((item) => {
          const noteKey = item.note || '';
          if (!itemsByNote.has(noteKey)) {
            itemsByNote.set(noteKey, []);
          }
          itemsByNote.get(noteKey)!.push({ note: item.note, payed: item.payed });
        });

        // Create separate groups for each note variant
        itemsByNote.forEach((items, noteKey) => {
          groups.push({
            product,
            items,
            note: noteKey || undefined,
            key: `${product.id}-${noteKey}`,
          });
        });
      });

      return groups;
    }, [order.content, productsMap]);

    const orderTotal = calculateOrderTotal(order);

    return (
      <div className="p-6 bg-gray-50 h-full overflow-y-auto">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Bestelldetails</h2>

          {/* Order Info */}
          <div className="grid grid-cols-2 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <p className="text-sm font-medium text-gray-500">Bestellt von</p>
              <p className="text-base text-gray-900">Admin</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Tisch</p>
              <p className="text-base text-gray-900">{table?.name || '-'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Datum & Zeit</p>
              <p className="text-base text-gray-900">
                {new Date(order.createdAt).toLocaleString('de-DE', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                })}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Anzahl Produkte</p>
              <p className="text-base text-gray-900">{getTotalProductCount(order)}</p>
            </div>
          </div>

          {/* Order Items */}
          <div className="space-y-4 mb-6">
            <h3 className="text-lg font-medium text-gray-900">Bestellte Artikel</h3>
            {groupedItems.map((group) => {
              return (
                <div key={group.key} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{group.product.name}</h4>
                      {group.note && (
                        <p className="text-sm text-gray-600 italic mt-1">{group.note}</p>
                      )}
                      <p className="text-sm text-gray-500 mt-1">
                        {group.items.length}x à {PriceCentUtils.toMoneyString(group.product.priceCents)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {PriceCentUtils.toMoneyString(group.product.priceCents * group.items.length)}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Order Total */}
          <div className="border-t border-gray-200 pt-4">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-900">Gesamtsumme:</span>
              <span className="text-xl font-bold text-gray-900">
                {PriceCentUtils.toMoneyString(orderTotal)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex">
      {/* Left Column - Orders Table */}
      <div className="flex-1 divide-y divide-gray-200 overflow-hidden rounded-l-lg bg-white shadow-sm grid grid-cols-1 grid-rows-[auto_1fr]">
        <div className="px-4 py-5 sm:px-6">
          <div className="sm:flex sm:items-center">
            <div className="sm:flex-auto">
              <h1 className="text-base font-semibold text-gray-900">Bestellungen</h1>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-hidden">
          <div className="ag-theme-quartz h-full">
            <AgGridReact
              rowData={orders}
              columnDefs={columnDefs}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true,
              }}
              pagination={true}
              paginationPageSize={10}
              domLayout="normal"
              getRowId={(params) => params.data.id}
              onRowClicked={onRowClicked}
              rowSelection="single"
            />
          </div>
        </div>
      </div>

      {/* Right Column - Order Details */}
      <div className="flex-1 border-l border-gray-200">
        {selectedOrder ? (
          <OrderDetail order={selectedOrder} />
        ) : (
          <div className="h-full flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <p className="text-gray-500 text-lg">Wählen Sie eine Bestellung aus</p>
              <p className="text-gray-400 text-sm mt-2">Klicken Sie auf eine Zeile in der Tabelle</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default AdminOrderPage;
