import { useState, useCallback } from "react";
import { usePrinters } from "@/data/usePrinters";
import { Button } from "primereact/button";
import type {
  ColDef,
  ICellRendererParams,
  CellValueChangedEvent,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Printer } from "@kassierer/shared/model";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

ModuleRegistry.registerModules([AllCommunityModule]);

// Temporary printer type for local state
type TempPrinter = {
  id: string;
  name: string;
  isTemporary: true;
};

// Combined type for table data
type TablePrinter = (Printer & { isTemporary?: false }) | TempPrinter;

// Update type for temporary printers
type TempPrinterUpdate = Partial<Omit<TempPrinter, "id" | "isTemporary">>;

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
  onDeleteTemp,
}: {
  data: TablePrinter;
  onDelete: (printer: Printer) => void;
  onDeleteTemp: (id: string) => void;
}) => {
  const isTemporary = "isTemporary" in data && data.isTemporary;

  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => {
          if (isTemporary) {
            onDeleteTemp(data.id);
          } else {
            onDelete(data as Printer);
          }
        }}
      />
    </div>
  );
};

function AdminPrintersPage() {
  const printers = usePrinters((state) => state.printers);
  const deletePrinter = usePrinters((state) => state.deletePrinter);
  const createPrinter = usePrinters((state) => state.createPrinter);
  const setPrinter = usePrinters((state) => state.setPrinter);

  // Local state for temporary printers
  const [tempPrinters, setTempPrinters] = useState<TempPrinter[]>([]);

  // Combine real and temporary printers for display
  const allPrinters: TablePrinter[] = [...tempPrinters, ...printers];

  // Helper functions
  const addTempPrinter = useCallback(() => {
    const newTempPrinter: TempPrinter = {
      id: crypto.randomUUID(),
      name: "",
      isTemporary: true,
    };
    setTempPrinters((prev) => [newTempPrinter, ...prev]);
  }, []);

  const updateTempPrinter = useCallback(
    (id: string, updates: TempPrinterUpdate) => {
      setTempPrinters((prev) =>
        prev.map((printer) =>
          printer.id === id ? { ...printer, ...updates } : printer,
        ),
      );
    },
    [],
  );

  const deleteTempPrinter = useCallback((id: string) => {
    setTempPrinters((prev) => prev.filter((printer) => printer.id !== id));
  }, []);

  const saveTempPrinter = useCallback(
    (tempPrinter: TempPrinter) => {
      if (!tempPrinter.name.trim()) {
        return; // Don't save empty names
      }

      // Create the real printer
      createPrinter({
        name: tempPrinter.name.trim(),
      });

      // Remove from temp list
      deleteTempPrinter(tempPrinter.id);
    },
    [createPrinter, deleteTempPrinter],
  );

  // Handle cell value changes
  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<TablePrinter>) => {
      const { data, colDef, newValue } = event;
      const field = colDef.field as keyof TablePrinter;

      if (!data) return;

      const isTemporary = "isTemporary" in data && data.isTemporary;

      if (isTemporary) {
        const tempPrinter = data as TempPrinter;
        updateTempPrinter(tempPrinter.id, { [field]: newValue });

        // Auto-save when name is filled
        if (field === "name" && newValue?.trim()) {
          saveTempPrinter({ ...tempPrinter, [field]: newValue });
        }
      } else {
        // Update existing printer
        const printer = data as Printer;
        setPrinter(printer.id, { ...printer, [field]: newValue });
      }
    },
    [updateTempPrinter, saveTempPrinter, setPrinter],
  );

  const columnDefs: ColDef<TablePrinter>[] = [
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      sortable: true,
      filter: true,
      editable: true,
      cellClass: (params) => {
        const isTemporary = params.data && "isTemporary" in params.data && params.data.isTemporary;
        return isTemporary ? "bg-yellow-50" : "";
      },
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<TablePrinter>) => (
        <ActionCellRenderer
          data={params.data!}
          onDelete={(printer) => deletePrinter(printer.id)}
          onDeleteTemp={deleteTempPrinter}
        />
      ),
      sortable: false,
      filter: false,
    },
  ];

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Drucker</h1>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              className="block rounded-md cursor-pointer bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              onClick={addTempPrinter}
            >
              Erstellen
            </button>
          </div>
        </div>
      </div>
      <div>
        <div className="ag-theme-quartz">
          <AgGridReact
            rowData={allPrinters}
            columnDefs={columnDefs}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true,
            }}
            pagination={true}
            paginationPageSize={10}
            domLayout="autoHeight"
            getRowId={(params) => params.data.id}
            onCellValueChanged={onCellValueChanged}
            stopEditingWhenCellsLoseFocus={true}
            singleClickEdit={true}
          />
        </div>
      </div>
    </div>
  );
}

export default AdminPrintersPage;
