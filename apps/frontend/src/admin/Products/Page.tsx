import { useState, useCallback } from "react";
import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import { usePrinters } from "@/data/usePrinters";
import { <PERSON><PERSON> } from "primereact/button";
import type {
  ColDef,
  ICellRendererParams,
  CellValueChangedEvent,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Product, Printer } from "@kassierer/shared/model";
import { PriceCentUtils } from "@/utils/priceCents";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

ModuleRegistry.registerModules([AllCommunityModule]);

// Temporary product type for local state
type TempProduct = {
  id: string;
  name: string;
  priceCents: number | null;
  categoryId: string | null;
  printerIds: string[];
  isTemporary: true;
};

// Combined type for table data
type TableProduct = (Product & { isTemporary?: false }) | TempProduct;

// Update type for temporary products
type TempProductUpdate = Partial<Omit<TempProduct, "id" | "isTemporary">>;

// Printer cell renderer component
const PrinterCellRenderer = ({
  data,
  printers,
  onPrinterChange,
}: {
  data: TableProduct;
  printers: Printer[];
  onPrinterChange: (productId: string, printerIds: string[]) => void;
}) => {
  const currentPrinterIds = data.printerIds || [];
  const hasNoPrinters = currentPrinterIds.length === 0;

  const handlePrinterToggle = (printerId: string) => {
    const newPrinterIds = currentPrinterIds.includes(printerId)
      ? currentPrinterIds.filter(id => id !== printerId)
      : [...currentPrinterIds, printerId];
    onPrinterChange(data.id, newPrinterIds);
  };

  return (
    <div className={`flex flex-wrap gap-1 ${hasNoPrinters ? 'border border-red-300 bg-red-50 p-1 rounded' : ''}`}>
      {printers.map((printer) => (
        <Button
          key={printer.id}
          label={printer.name}
          size="small"
          severity={currentPrinterIds.includes(printer.id) ? "info" : "secondary"}
          outlined={!currentPrinterIds.includes(printer.id)}
          onClick={() => handlePrinterToggle(printer.id)}
          className="text-xs"
        />
      ))}
      {hasNoPrinters && (
        <span className="text-red-600 text-xs font-medium">Kein Drucker zugewiesen!</span>
      )}
    </div>
  );
};

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
  onDeleteTemp,
}: {
  data: TableProduct;
  onDelete: (product: Product) => void;
  onDeleteTemp: (id: string) => void;
}) => {
  const isTemporary = "isTemporary" in data && data.isTemporary;

  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => {
          if (isTemporary) {
            onDeleteTemp(data.id);
          } else {
            onDelete(data as Product);
          }
        }}
      />
    </div>
  );
};

function AdminProductsPage() {
  const products = useProducts((state) => state.products);
  const deleteProduct = useProducts((state) => state.deleteProduct);
  const createProduct = useProducts((state) => state.createProduct);
  const setProduct = useProducts((state) => state.setProduct);
  const categories = useProductCategories((state) => state.categories);
  const printers = usePrinters((state) => state.printers);

  // Local state for temporary products
  const [tempProducts, setTempProducts] = useState<TempProduct[]>([]);

  // Combine real and temporary products for display
  const allProducts: TableProduct[] = [...tempProducts, ...products];

  // Helper functions
  const addTempProduct = useCallback(() => {
    const newTempProduct: TempProduct = {
      id: crypto.randomUUID(),
      name: "",
      priceCents: null,
      categoryId: null,
      printerIds: [],
      isTemporary: true,
    };
    setTempProducts((prev) => [newTempProduct, ...prev]);
  }, []);

  const updateTempProduct = useCallback(
    (id: string, updates: TempProductUpdate) => {
      setTempProducts((prev) =>
        prev.map((product) =>
          product.id === id ? { ...product, ...updates } : product,
        ),
      );
    },
    [],
  );

  const saveTempProduct = useCallback(
    (tempProduct: TempProduct) => {
      if (
        tempProduct.name &&
        tempProduct.priceCents !== null &&
        tempProduct.categoryId
      ) {
        createProduct({
          name: tempProduct.name,
          priceCents: tempProduct.priceCents,
          categoryId: tempProduct.categoryId,
          printerIds: tempProduct.printerIds,
        });
        setTempProducts((prev) => prev.filter((p) => p.id !== tempProduct.id));
      }
    },
    [createProduct],
  );

  const deleteTempProduct = useCallback((id: string) => {
    setTempProducts((prev) => prev.filter((p) => p.id !== id));
  }, []);

  const handlePrinterChange = useCallback(
    (productId: string, printerIds: string[]) => {
      // Check if it's a temporary product
      const tempProduct = tempProducts.find(p => p.id === productId);
      if (tempProduct) {
        updateTempProduct(productId, { printerIds });
        return;
      }

      // Update existing product
      const product = products.find(p => p.id === productId);
      if (product) {
        setProduct(productId, { ...product, printerIds });
      }
    },
    [tempProducts, products, updateTempProduct, setProduct],
  );

  // Handle cell value changes
  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<TableProduct>) => {
      const { data, colDef, newValue } = event;
      const isTemporary = "isTemporary" in data && data.isTemporary;

      if (!isTemporary) {
        const product = products.find((p) => p.id === data.id);
        if (product) {
          setProduct(data.id, {
            ...product,
            [colDef.field!]: newValue,
          });
        }
        return;
      }

      if (isTemporary) {
        const field = colDef.field;
        const updates: TempProductUpdate = {};

        if (field === "name") {
          updates.name = newValue;
        } else if (field === "priceCents") {
          updates.priceCents = newValue;
        } else if (field === "categoryId") {
          updates.categoryId = newValue;
        } else if (field === "printerIds") {
          updates.printerIds = newValue;
        }

        updateTempProduct(data.id, updates);

        // Check if product should be saved
        const updatedProduct = tempProducts.find((p) => p.id === data.id);
        if (updatedProduct) {
          const finalProduct = { ...updatedProduct, ...updates };
          if (
            finalProduct.name &&
            finalProduct.priceCents !== null &&
            finalProduct.categoryId
          ) {
            saveTempProduct(finalProduct);
          }
        }
      }
    },
    [updateTempProduct, tempProducts, saveTempProduct, products, setProduct],
  );

  const columnDefs: ColDef<TableProduct>[] = [
    {
      headerName: "Status",
      field: "isTemporary",
      width: 120,
      valueFormatter: (params) => {
        const isTemporary = params?.data?.isTemporary;
        const printerIds = params?.data?.printerIds || [];
        const hasNoPrinters = printerIds.length === 0;

        if (isTemporary) return "Neu";
        if (hasNoPrinters) return "Fehler";
        return "OK";
      },
      cellClass: (params) => {
        const printerIds = params?.data?.printerIds || [];
        const hasNoPrinters = printerIds.length === 0;
        const isTemporary = params?.data?.isTemporary;

        if (hasNoPrinters && !isTemporary) return "bg-red-100 text-red-800";
        if (isTemporary) return "bg-yellow-100 text-yellow-800";
        return "bg-green-100 text-green-800";
      },
      cellEditor: "agTextCellEditor",
      sortable: false,
      filter: false,
      editable: false,
    },
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      editable: true,
      sortable: false,
      filter: true,
    },
    {
      headerName: "Preis",
      field: "priceCents",
      width: 150,
      valueFormatter: (params) => {
        return params.value !== null
          ? PriceCentUtils.toMoneyString(params.value)
          : "-";
      },
      editable: true,
      cellEditor: "agNumberCellEditor",
      cellEditorParams: {
        min: 0,
        precision: 0,
      },
      sortable: true,
      filter: "agNumberColumnFilter",
    },
    {
      headerName: "Kategorie",
      field: "categoryId",
      width: 200,
      editable: true,
      cellEditor: "agSelectCellEditor",
      cellEditorParams: {
        values: categories.map((cat) => cat.id),
      },
      valueFormatter: (params) => {
        const category = categories.find((cat) => cat.id === params.value);
        return category ? category.name : "";
      },
      sortable: true,
      filter: true,
    },
    {
      headerName: "Drucker",
      field: "printerIds",
      width: 300,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <PrinterCellRenderer
          data={params.data!}
          printers={printers}
          onPrinterChange={handlePrinterChange}
        />
      ),
      sortable: false,
      filter: false,
      editable: false,
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableProduct>) => (
        <ActionCellRenderer
          data={params.data!}
          onDelete={({ id }) => deleteProduct(id)}
          onDeleteTemp={deleteTempProduct}
        />
      ),
      sortable: false,
      filter: false,
      editable: false,
    },
  ];

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        {/* Content goes here */}
        {/* We use less vertical padding on card headers on desktop than on body sections */}

        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Produkte</h1>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              className="block rounded-md cursor-pointer bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              onClick={addTempProduct}
            >
              Erstellen
            </button>
          </div>
        </div>
      </div>
      <div>
        <div
          className="ag-theme-quartz"
        >
          <AgGridReact
            rowData={allProducts}
            columnDefs={columnDefs}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true,
            }}
            pagination={true}
            paginationPageSize={10}
            domLayout="autoHeight"
            getRowId={(params) => params.data.id}
            onCellValueChanged={onCellValueChanged}
          />
        </div>
      </div>
    </div>
  );
}

export default AdminProductsPage;
