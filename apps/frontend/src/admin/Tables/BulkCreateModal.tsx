import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ModalButton } from '@/admin/ui'

interface BulkCreateModalProps {
  open: boolean
  onClose: (open: boolean) => void
  onCreateTables: (tableNames: string[]) => void
}

// Utility function to parse table creation input
function parseTableInput(input: string): string[] {
  const tableNames: string[] = []
  
  // Split by comma and process each part
  const parts = input.split(',').map(part => part.trim()).filter(Boolean)
  
  for (const part of parts) {
    // Check if it's a range pattern like "1-4" or "Table 1 - Table 4"
    const rangeMatch = part.match(/^(.+?)\s*-\s*(.+?)$/)
    
    if (rangeMatch) {
      const [, start, end] = rangeMatch
      
      // Check if it's a numeric range like "1-4"
      const startNum = parseInt(start.trim())
      const endNum = parseInt(end.trim())
      
      if (!isNaN(startNum) && !isNaN(endNum) && startNum <= endNum) {
        // Pure numeric range
        for (let i = startNum; i <= endNum; i++) {
          tableNames.push(i.toString())
        }
      } else {
        // Check if it's a pattern like "Table 1 - Table 4"
        const startMatch = start.match(/^(.+?)\s*(\d+)$/)
        const endMatch = end.match(/^(.+?)\s*(\d+)$/)
        
        if (startMatch && endMatch) {
          const [, startPrefix, startNumStr] = startMatch
          const [, endPrefix, endNumStr] = endMatch
          const startNumber = parseInt(startNumStr)
          const endNumber = parseInt(endNumStr)
          
          // Check if prefixes match and numbers are valid
          if (startPrefix.trim() === endPrefix.trim() && 
              !isNaN(startNumber) && !isNaN(endNumber) && 
              startNumber <= endNumber) {
            for (let i = startNumber; i <= endNumber; i++) {
              tableNames.push(`${startPrefix.trim()} ${i}`)
            }
          } else {
            // If pattern doesn't match, treat as literal names
            tableNames.push(start.trim(), end.trim())
          }
        } else {
          // If no numeric pattern found, treat as literal names
          tableNames.push(start.trim(), end.trim())
        }
      }
    } else {
      // Single table name
      tableNames.push(part)
    }
  }
  
  return tableNames
}

export const BulkCreateModal: React.FC<BulkCreateModalProps> = ({
  open,
  onClose,
  onCreateTables
}) => {
  const [input, setInput] = useState('')
  const [preview, setPreview] = useState<string[]>([])

  const handleInputChange = (value: string) => {
    setInput(value)
    if (value.trim()) {
      try {
        const parsed = parseTableInput(value)
        setPreview(parsed.slice(0, 10)) // Show max 10 for preview
      } catch {
        setPreview([])
      }
    } else {
      setPreview([])
    }
  }

  const handleCreate = () => {
    if (input.trim()) {
      const tableNames = parseTableInput(input)
      onCreateTables(tableNames)
      setInput('')
      setPreview([])
      onClose(false)
    }
  }

  const handleClose = () => {
    setInput('')
    setPreview([])
    onClose(false)
  }

  return (
    <Modal
      open={open}
      onClose={handleClose}
      title="Mehrere Tische erstellen"
      size="lg"
    >
      <div className="space-y-4">
        <div>
          <p className="text-sm text-gray-600 mb-3">
            Erstellen Sie mehrere Tische auf einmal mit verschiedenen Mustern:
          </p>
          <ul className="text-xs text-gray-500 space-y-1 mb-4">
            <li>• <code className="bg-gray-100 px-1 rounded">1-4</code> erstellt Tische: 1, 2, 3, 4</li>
            <li>• <code className="bg-gray-100 px-1 rounded">Table 1 - Table 4</code> erstellt: Table 1, Table 2, Table 3, Table 4</li>
            <li>• <code className="bg-gray-100 px-1 rounded">1-3, 8-11</code> erstellt: 1, 2, 3, 8, 9, 10, 11</li>
          </ul>
        </div>

        <div>
          <label htmlFor="table-input" className="block text-sm font-medium text-gray-700 mb-2">
            Tisch-Muster eingeben
          </label>
          <input
            id="table-input"
            type="text"
            value={input}
            onChange={(e) => handleInputChange(e.target.value)}
            placeholder="z.B. 1-10 oder Table 1 - Table 5"
            className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
          />
        </div>

        {preview.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Vorschau ({preview.length} Tische{parseTableInput(input).length > 10 ? `, ${parseTableInput(input).length - 10} weitere...` : ''}):
            </label>
            <div className="bg-gray-50 rounded-md p-3 max-h-32 overflow-y-auto">
              <div className="flex flex-wrap gap-1">
                {preview.map((name, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10"
                  >
                    {name}
                  </span>
                ))}
                {parseTableInput(input).length > 10 && (
                  <span className="inline-flex items-center rounded-md bg-gray-50 px-2 py-1 text-xs font-medium text-gray-500">
                    +{parseTableInput(input).length - 10} weitere
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      <ModalActions>
        <ModalButton variant="secondary" onClick={handleClose}>
          Abbrechen
        </ModalButton>
        <ModalButton 
          variant="primary" 
          onClick={handleCreate}
          disabled={!input.trim() || preview.length === 0}
        >
          {preview.length > 0 ? `${parseTableInput(input).length} Tische erstellen` : 'Erstellen'}
        </ModalButton>
      </ModalActions>
    </Modal>
  )
}
