import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "primereact/button";
import type { ColDef, ICellRendererParams } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { ClientInfo } from "@kassierer/shared/auth";
import { ClientService } from "./client.service";
import { TokenGenerationModal } from "./TokenGenerationModal";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

ModuleRegistry.registerModules([AllCommunityModule]);

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDeactivate,
}: {
  data: ClientInfo;
  onDeactivate: (client: ClientInfo) => void;
}) => {
  return (
    <div className="flex gap-2">
      {data.isActive && (
        <Button
          label="Deaktivieren"
          icon="pi pi-ban"
          severity="warning"
          size="small"
          onClick={() => onDeactivate(data)}
        />
      )}
    </div>
  );
};

function AdminClientsPage() {
  const [clients, setClients] = useState<ClientInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [tokenModalOpen, setTokenModalOpen] = useState(false);

  // Load clients on component mount
  useEffect(() => {
    loadClients();
  }, []);

  const loadClients = async () => {
    setLoading(true);
    try {
      const clientsData = await ClientService.getClients();
      setClients(clientsData);
    } catch (error) {
      console.error('Failed to load clients:', error);
      // TODO: Show error message
    } finally {
      setLoading(false);
    }
  };

  const handleDeactivateClient = useCallback(async (client: ClientInfo) => {
    try {
      await ClientService.deactivateClient(client.id);
      // Reload clients to reflect the change
      await loadClients();
    } catch (error) {
      console.error('Failed to deactivate client:', error);
      // TODO: Show error message
    }
  }, []);

  const handleTokenModalClose = (open: boolean) => {
    setTokenModalOpen(open);
    if (!open) {
      // Reload clients when modal closes (in case a new client was created)
      loadClients();
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const columnDefs: ColDef<ClientInfo>[] = [
    {
      headerName: "Client ID",
      field: "id",
      width: 120,
      cellRenderer: (params: ICellRendererParams<ClientInfo>) => (
        <code className="text-xs bg-gray-100 px-2 py-1 rounded">
          {params.value}
        </code>
      ),
    },
    {
      headerName: "Status",
      field: "isActive",
      width: 100,
      cellRenderer: (params: ICellRendererParams<ClientInfo>) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            params.value
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {params.value ? "Aktiv" : "Inaktiv"}
        </span>
      ),
    },
    {
      headerName: "Erstellt am",
      field: "createdAt",
      width: 180,
      cellRenderer: (params: ICellRendererParams<ClientInfo>) =>
        params.value ? formatDate(params.value) : "-",
    },
    {
      headerName: "Zuletzt aktiv",
      field: "lastActiveAt",
      width: 180,
      cellRenderer: (params: ICellRendererParams<ClientInfo>) =>
        params.value ? formatDate(params.value) : "Nie",
    },
    {
      headerName: "Name",
      field: "name",
      width: 150,
      cellRenderer: (params: ICellRendererParams<ClientInfo>) =>
        params.value || "-",
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<ClientInfo>) => (
        <ActionCellRenderer
          data={params.data!}
          onDeactivate={handleDeactivateClient}
        />
      ),
      sortable: false,
      filter: false,
      editable: false,
    },
  ];

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Clients</h1>
            <p className="mt-2 text-sm text-gray-700">
              Verwalten Sie Client-Zugänge für Ihr System. Generieren Sie temporäre Tokens für neue Clients.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              className="block rounded-md cursor-pointer bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
              onClick={() => setTokenModalOpen(true)}
            >
              Token Generieren
            </button>
          </div>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Lade Clients...</p>
          </div>
        ) : (
          <div className="ag-theme-quartz">
            <AgGridReact
              rowData={clients}
              columnDefs={columnDefs}
              defaultColDef={{
                resizable: true,
                sortable: true,
                filter: true,
              }}
              pagination={true}
              paginationPageSize={20}
              domLayout="autoHeight"
              getRowId={(params) => params.data.id}
              noRowsOverlayComponent={() => (
                <div className="text-center py-8">
                  <p className="text-gray-500">Keine Clients vorhanden</p>
                  <p className="text-sm text-gray-400 mt-1">
                    Generieren Sie einen Token, um den ersten Client zu erstellen
                  </p>
                </div>
              )}
            />
          </div>
        )}
      </div>

      <TokenGenerationModal
        open={tokenModalOpen}
        onClose={handleTokenModalClose}
      />
    </div>
  );
}

export default AdminClientsPage;
