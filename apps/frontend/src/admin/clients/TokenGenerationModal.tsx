import { useState, useEffect } from 'react';
import { Modal, ModalActions, ModalButton } from '@/admin/ui/Modal';
import { ClientService } from './client.service';
import { ClipboardDocumentIcon, CheckIcon } from '@heroicons/react/24/outline';
import type { GenerateTokenResponse } from '@kassierer/shared/auth';

interface TokenGenerationModalProps {
  open: boolean;
  onClose: (open: boolean) => void;
}

export const TokenGenerationModal: React.FC<TokenGenerationModalProps> = ({
  open,
  onClose,
}) => {
  const [tokenData, setTokenData] = useState<GenerateTokenResponse | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [copied, setCopied] = useState(false);

  // Generate token when modal opens
  useEffect(() => {
    if (open && !tokenData) {
      generateToken();
    }
  }, [open, tokenData]);

  // Countdown timer
  useEffect(() => {
    if (!tokenData) return;

    const expiresAt = new Date(tokenData.expiresAt).getTime();
    
    const updateTimer = () => {
      const now = Date.now();
      const remaining = Math.max(0, Math.floor((expiresAt - now) / 1000));
      setTimeRemaining(remaining);
      
      if (remaining === 0) {
        // Token expired, close modal
        handleClose();
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [tokenData]);

  const generateToken = async () => {
    setIsGenerating(true);
    try {
      const response = await ClientService.generateTemporaryToken();
      setTokenData(response);
    } catch (error) {
      console.error('Failed to generate token:', error);
      // TODO: Show error message
    } finally {
      setIsGenerating(false);
    }
  };

  const handleClose = () => {
    setTokenData(null);
    setTimeRemaining(0);
    setCopied(false);
    onClose(false);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const clientUrl = tokenData 
    ? `${window.location.origin}/client-login?token=${tokenData.token}`
    : '';

  return (
    <Modal
      open={open}
      onClose={handleClose}
      title="Generate Client Token"
      size="md"
    >
      <div className="space-y-4">
        {isGenerating ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">Generating token...</p>
          </div>
        ) : tokenData ? (
          <>
            {/* Token Display */}
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                6-Character Token
              </label>
              <div className="flex items-center space-x-2">
                <code className="flex-1 text-2xl font-mono font-bold text-center py-3 px-4 bg-white border rounded-md tracking-widest">
                  {tokenData.token}
                </code>
                <button
                  onClick={() => copyToClipboard(tokenData.token)}
                  className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md"
                  title="Copy token"
                >
                  {copied ? (
                    <CheckIcon className="h-5 w-5 text-green-500" />
                  ) : (
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Countdown Timer */}
            <div className="text-center">
              <div className="text-sm text-gray-600">Token expires in</div>
              <div className={`text-2xl font-bold ${
                timeRemaining < 60 ? 'text-red-600' : 'text-gray-900'
              }`}>
                {formatTime(timeRemaining)}
              </div>
            </div>

            {/* Shareable Link */}
            <div className="bg-gray-50 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Shareable Link
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={clientUrl}
                  readOnly
                  className="flex-1 text-sm px-3 py-2 bg-white border rounded-md text-gray-600"
                />
                <button
                  onClick={() => copyToClipboard(clientUrl)}
                  className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md"
                  title="Copy link"
                >
                  {copied ? (
                    <CheckIcon className="h-5 w-5 text-green-500" />
                  ) : (
                    <ClipboardDocumentIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Instructions</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Share the 6-character token or link with the client</li>
                <li>• Token expires in 5 minutes</li>
                <li>• Token can only be used once</li>
                <li>• Client will get 1-week access after using the token</li>
              </ul>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-sm text-gray-600">Failed to generate token. Please try again.</p>
          </div>
        )}
      </div>

      <ModalActions>
        <ModalButton variant="secondary" onClick={handleClose}>
          Close
        </ModalButton>
        {!tokenData && !isGenerating && (
          <ModalButton onClick={generateToken}>
            Try Again
          </ModalButton>
        )}
      </ModalActions>
    </Modal>
  );
};
