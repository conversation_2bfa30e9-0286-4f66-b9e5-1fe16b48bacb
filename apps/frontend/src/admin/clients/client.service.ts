import { axiosInstance } from "@/utils/axios";
import type { 
  ClientInfo, 
  GenerateTokenResponse, 
  ClientLoginRequest, 
  ClientLoginResponse 
} from "@kassierer/shared/auth";

async function getClients(): Promise<ClientInfo[]> {
  const { data } = await axiosInstance.get<ClientInfo[]>("/auth/clients");
  return data;
}

async function generateTemporaryToken(): Promise<GenerateTokenResponse> {
  const { data } = await axiosInstance.post<GenerateTokenResponse>("/auth/temporary-token");
  return data;
}

async function deactivateClient(clientId: string): Promise<void> {
  await axiosInstance.delete(`/auth/clients/${clientId}`);
}

async function clientLogin(token: string): Promise<ClientLoginResponse> {
  const { data } = await axiosInstance.post<ClientLoginResponse>("/auth/client-login", {
    token,
  } as ClientLoginRequest);
  return data;
}

export const ClientService = {
  getClients,
  generateTemporaryToken,
  deactivateClient,
  clientLogin,
};
