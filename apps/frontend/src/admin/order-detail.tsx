import { useState } from "react";
import { Orders } from "@kassierer/shared/data/orders";
import { useYjsStore } from "@/stores/yjsStore";
import type { Order, OrderItem } from "@kassierer/shared/model";
import { Products } from "@kassierer/shared/data/products";
import { useParams } from "react-router-dom";
import { Card } from 'primereact/card';
import { Button } from "primereact/button";

function AdminOrderDetail() {
  const { orderId } = useParams();

  const { doc } = useYjsStore();

  if (!doc || !orderId) {
    return <div>Loading...</div>;
  }

  const { getOne: getOrder, setOne, observe } = Orders(doc);
  const initialOrder = getOrder(orderId);
  const [order, setOrder] = useState<Order>(initialOrder || {
    id: orderId,
    content: [],
    createdAt: "",
    tableId: "",
    userId: 1,
    payed: false,
  });
  observe(() => {
    const updatedOrder = getOrder(orderId);
    if (updatedOrder) {
      setOrder(updatedOrder);
    }
  });

  const { getOne: getProduct } = Products(doc);

  const payItem = (productId: string, itemIndex: number) => {
    setOne(orderId, {
      ...order,
      content: order.content.map((item) => {
        if (item.productId === productId) {
          return {
            ...item,
            items: item.items.map<OrderItem>((oItem, i) =>
              i === itemIndex ? { ...oItem, payed: true } : oItem,
            ),
          };
        }
        return item;
      }),
    });
  };

  if (!order) {
    return <div>Loading</div>;
  }

  return (
    <div className="grid">
      <div className="col-12">
        <Card title={`Bestellung: ${order.createdAt}`}>
          <div className="grid">
            {order.content.map((item) => (
              <div key={item.productId} className="col-12 mb-4">
                <h5 className="mb-2">
                  {getProduct(item.productId)?.name || "Unknown Product"} ({item.items.length})
                </h5>
                <div className="grid">
                  {item.items.map((pItem, i) => (
                    <div key={i} className="col-12 md:col-6 lg:col-4 mb-2">
                      <div className="flex align-items-center gap-2">
                        <span>Bezahlt:</span>
                        <Button
                          label={pItem.payed ? "Ja" : "Nein"}
                          severity={pItem.payed ? "success" : "warning"}
                          size="small"
                          onClick={() => payItem(item.productId, i)}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}

export default AdminOrderDetail;
