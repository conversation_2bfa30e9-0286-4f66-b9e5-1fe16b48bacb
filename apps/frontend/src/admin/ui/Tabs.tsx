import { ChevronDownIcon } from '@heroicons/react/16/solid'
import { classes } from '@/utils/classes'

export interface Tab {
  name: string
  href?: string
  current: boolean
  onClick?: () => void
}

export interface TabsProps {
  tabs: Tab[]
  onTabChange?: (tab: Tab) => void
  className?: string
  'aria-label'?: string
}

export const Tabs: React.FC<TabsProps> = ({
  tabs,
  onTabChange,
  className,
  'aria-label': ariaLabel = 'Tabs'
}) => {
  const currentTab = tabs.find((tab) => tab.current)

  const handleTabClick = (tab: Tab) => {
    if (tab.onClick) {
      tab.onClick()
    }
    if (onTabChange) {
      onTabChange(tab)
    }
  }

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedTab = tabs.find((tab) => tab.name === event.target.value)
    if (selectedTab) {
      handleTabClick(selectedTab)
    }
  }

  return (
    <div className={className}>
      {/* Mobile select dropdown */}
      <div className="grid grid-cols-1 sm:hidden">
        <select
          value={currentTab?.name || ''}
          onChange={handleSelectChange}
          aria-label={ariaLabel}
          className="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-2 pr-8 pl-3 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600"
        >
          {tabs.map((tab) => (
            <option key={tab.name} value={tab.name}>
              {tab.name}
            </option>
          ))}
        </select>
        <ChevronDownIcon
          aria-hidden="true"
          className="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end fill-gray-500"
        />
      </div>

      {/* Desktop tab navigation */}
      <div className="hidden sm:block">
        <div className="border-b border-gray-200">
          <nav aria-label={ariaLabel} className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const TabElement = tab.href ? 'a' : 'button'
              
              return (
                <TabElement
                  key={tab.name}
                  href={tab.href}
                  onClick={tab.href ? undefined : () => handleTabClick(tab)}
                  aria-current={tab.current ? 'page' : undefined}
                  className={classes(
                    tab.current
                      ? 'border-indigo-500 text-indigo-600'
                      : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                    'border-b-2 px-1 py-4 text-sm font-medium whitespace-nowrap',
                    !tab.href ? 'bg-transparent' : undefined
                  )}
                >
                  {tab.name}
                </TabElement>
              )
            })}
          </nav>
        </div>
      </div>
    </div>
  )
}
