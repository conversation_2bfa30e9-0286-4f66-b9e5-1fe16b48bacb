import { useUserStore } from "@/stores/userStore";
import { type PropsWithChildren, useEffect } from "react";
import { useNavigate } from "react-router-dom";

export const ClientGuard: React.FC<PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();
  const isClient = useUserStore((state) => !state.user?.email);
  const isAuthPending = useUserStore((state) => state.isAuthPending);
  const token = useUserStore((state) => state.token);

  useEffect(() => {
    if (!isAuthPending) {
      if (!token) {
        // No token, redirect to client login
        navigate(`/client-login`);
      }
    }
  }, [isAuthPending, token, isClient, navigate]);

  if (isAuthPending) {
    return (
      <div className="flex min-h-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Laden...</p>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="flex min-h-full items-center justify-center">
        <div className="text-center">
          <p className="text-sm text-gray-600">Nicht autorisiert</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
