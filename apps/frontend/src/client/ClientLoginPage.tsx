import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useUserStore } from "@/stores/userStore";

export default function ClientLoginPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isClient = useUserStore((state) => !state.user?.email);
  const isAuthPending = useUserStore((state) => state.isAuthPending);
  const token = useUserStore((state) => state.token);
  const clientLogin = useUserStore((state) => state.clientLogin);
  
  const [tokenInput, setTokenInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // Get token from URL parameters if available
  const urlToken = searchParams.get("token");

  useEffect(() => {
    // If user is already authenticated as a client, redirect to kassierer
    if (!isAuthPending && token && isClient) {
      navigate("/app/kassierer");
      return;
    }

    // If token is provided in URL, auto-fill the input
    if (urlToken) {
      setTokenInput(urlToken);
    }
  }, [token, isAuthPending, isClient, urlToken, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!tokenInput.trim()) {
      setError("Bitte geben Sie einen Token ein");
      return;
    }

    if (tokenInput.length !== 6) {
      setError("Token muss genau 6 Zeichen lang sein");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      await clientLogin(tokenInput.toUpperCase());
      // Redirect to kassierer on successful login
      navigate("/kassierer");
    } catch {
      setError("Ungültiger oder abgelaufener Token. Bitte versuchen Sie es erneut.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, "");
    if (value.length <= 6) {
      setTokenInput(value);
      setError("");
    }
  };

  return (
    <div className="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-sm">
        <div className="mx-auto h-12 w-12 flex items-center justify-center bg-indigo-100 rounded-full">
          <svg
            className="h-6 w-6 text-indigo-600"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z"
            />
          </svg>
        </div>
        <h2 className="mt-6 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
          Client Zugang
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Geben Sie Ihren 6-stelligen Token ein, um sich anzumelden
        </p>
      </div>

      <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label
              htmlFor="token"
              className="block text-sm font-medium leading-6 text-gray-900"
            >
              6-stelliger Token
            </label>
            <div className="mt-2">
              <input
                id="token"
                name="token"
                type="text"
                value={tokenInput}
                onChange={handleTokenChange}
                placeholder="ABC123"
                maxLength={6}
                required
                autoComplete="off"
                className="block w-full rounded-md border-0 py-3 px-4 text-center text-2xl font-mono font-bold tracking-widest text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:leading-6 uppercase"
                disabled={isSubmitting}
              />
            </div>
            {error && (
              <p className="mt-2 text-sm text-red-600">{error}</p>
            )}
          </div>

          <div>
            <button
              type="submit"
              disabled={isSubmitting || tokenInput.length !== 6}
              className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold leading-6 text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-solid focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Anmelden...
                </div>
              ) : (
                "Anmelden"
              )}
            </button>
          </div>
        </form>

        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">Hinweise:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Token sind nur 5 Minuten gültig</li>
            <li>• Token können nur einmal verwendet werden</li>
            <li>• Nach erfolgreicher Anmeldung haben Sie 1 Woche Zugang</li>
            <li>• Bei Problemen wenden Sie sich an den Administrator</li>
          </ul>
        </div>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            Sind Sie Administrator?{" "}
            <a
              href="/login"
              className="font-semibold leading-6 text-indigo-600 hover:text-indigo-500"
            >
              Hier anmelden
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
