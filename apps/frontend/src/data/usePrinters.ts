import { useYjsStore } from "@/stores/yjsStore";
import { Printers } from "@kassierer/shared/data/printer";
import type { Printer } from "@kassierer/shared/model";
import { create } from "zustand";

type PrinterStore = {
  printers: Printer[];
  printersMap: Record<Printer["id"], Printer>;
  deletePrinter: (id: Printer["id"]) => void;
  createPrinter: (printer: Omit<Printer, "id">) => void;
  setPrinter: (id: Printer["id"], printer: Printer) => void;
  initialize: () => void;
};

export const usePrinters = create<PrinterStore>((set, get) => ({
  printers: [],
  get printersMap() {
    return get().printers.reduce<Record<Printer["id"], Printer>>(
      (pMap, printer) => {
        pMap[printer.id] = printer;
        return pMap;
      },
      {},
    );
  },
  deletePrinter: () => {},
  createPrinter: () => {},
  setPrinter: () => {},

  initialize: () => {
    const doc = useYjsStore.getState().doc;
    if (!doc) return;

    const { observe, getAll, deleteOne, createOne, setOne } = Printers(doc);
    observe(() => set({ printers: Array.from(getAll()) }));

    // Update the store methods
    set({
      printers: Array.from(getAll()),
      deletePrinter: deleteOne,
      createPrinter: createOne,
      setPrinter: setOne,
    });
  },
}));

// Subscribe to YJS store changes and reinitialize when doc changes
useYjsStore.subscribe((state) => {
  if (state.doc) {
    usePrinters.getState().initialize();
  }
});

// Initialize if doc is already available
if (useYjsStore.getState().doc) {
  usePrinters.getState().initialize();
}
