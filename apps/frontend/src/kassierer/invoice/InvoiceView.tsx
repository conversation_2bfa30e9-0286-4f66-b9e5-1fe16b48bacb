import { useProducts } from "@/data/useProducts";
import { useOrders } from "@/data/useOrders";
import { useTables } from "@/data/useTables";
import { useUserSettings } from "@/data/useUserSettings";
import type { Order, Product } from "@kassierer/shared/model";
import { useEffect, useState, useMemo, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { PriceCentUtils } from "@/utils/priceCents";

// Type for individual invoice items (flattened from orders)
type InvoiceItem = {
  id: string; // unique identifier for this specific item
  orderId: string;
  productId: string;
  product: Product;
  note?: string;
  payed: boolean;
  orderItemIndex: number; // index within the order's items array
  contentIndex: number; // index within the order's content array
};

// Helper function to check if all items in an order are paid
const isOrderFullyPaid = (order: Order): boolean => {
  return order.content.every(contentItem =>
    contentItem.items.every(item => item.payed)
  );
};

// Helper function to create invoice items from orders
const createInvoiceItemsFromOrders = (
  orders: Order[],
  tableId: string,
  productsMap: Record<string, Product>
): InvoiceItem[] => {
  const items: InvoiceItem[] = [];

  orders.forEach((order: Order) => {
    // Skip orders that are already fully paid or not for this table
    if (order.payed || order.tableId !== tableId) return;

    order.content.forEach((contentItem, contentIndex) => {
      const product = productsMap[contentItem.productId];
      if (product) {
        contentItem.items.forEach((orderItem, orderItemIndex) => {
          if (!orderItem.payed) {
            items.push({
              id: `${order.id}-${contentIndex}-${orderItemIndex}`,
              orderId: order.id,
              productId: contentItem.productId,
              product,
              note: orderItem.note,
              payed: orderItem.payed,
              orderItemIndex,
              contentIndex,
            });
          }
        });
      }
    });
  });

  return items;
};

// Helper function to create optimized order update map
const createOrderUpdateMap = (
  invoiceItems: InvoiceItem[],
  orders: Order[]
): Map<string, Order> => {
  const orderUpdates = new Map<string, Order>();
  const ordersMap = new Map(orders.map(order => [order.id, order]));

  invoiceItems.forEach(item => {
    const originalOrder = ordersMap.get(item.orderId);
    if (!originalOrder) return;

    // Get or create order update
    let updatedOrder = orderUpdates.get(item.orderId);
    if (!updatedOrder) {
      updatedOrder = { ...originalOrder };
      orderUpdates.set(item.orderId, updatedOrder);
    }

    // Mark the specific item as paid
    updatedOrder.content = updatedOrder.content.map((contentItem, contentIndex) => {
      if (contentIndex === item.contentIndex) {
        return {
          ...contentItem,
          items: contentItem.items.map((orderItem, orderItemIndex) => {
            if (orderItemIndex === item.orderItemIndex) {
              return { ...orderItem, payed: true };
            }
            return orderItem;
          }),
        };
      }
      return contentItem;
    });
  });

  return orderUpdates;
};

function InvoiceView() {
  const navigate = useNavigate();
  const { tableId } = useParams();

  // Settings
  const darkMode = useUserSettings((state) => state.darkMode);
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Data
  const products = useProducts((state) => state.products);
  const orders = useOrders((state) => state.orders);
  const updateOrder = useOrders((state) => state.updateOrder);
  const tables = useTables((state) => state.tables);

  // State for selected items (items to be paid)
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

  // Get products map for efficient lookup
  const productsMap = useMemo(() => {
    return products.reduce<Record<string, Product>>((pMap, product) => {
      pMap[product.id] = product;
      return pMap;
    }, {});
  }, [products]);

  // Memoized unpaid items for this table using optimized helper function
  const unpaidItems = useMemo(() => {
    return createInvoiceItemsFromOrders(orders, tableId || '', productsMap);
  }, [orders, tableId, productsMap]);

  // Get selected items for invoice
  const invoiceItems = useMemo(() => {
    return unpaidItems.filter((item) => selectedItems.has(item.id));
  }, [unpaidItems, selectedItems]);

  // Calculate total
  const invoiceTotal = useMemo(() => {
    return invoiceItems.reduce((total, item) => {
      return total + item.product.priceCents;
    }, 0);
  }, [invoiceItems]);

  // Get table name
  const table = tables.find((t) => t.id === tableId);

  // Select all items by default when component mounts
  useEffect(() => {
    const allItemIds = new Set(unpaidItems.map((item) => item.id));
    setSelectedItems(allItemIds);
  }, [unpaidItems]);

  // Optimized item click handler with useCallback
  const handleItemClick = useCallback((item: InvoiceItem) => {
    setSelectedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(item.id)) {
        newSet.delete(item.id);
      } else {
        newSet.add(item.id);
      }
      return newSet;
    });
  }, []);

  // Optimized reset handler with useCallback
  const handleReset = useCallback(() => {
    setSelectedItems(new Set());
  }, []);

  // Handle payment
  // Optimized payment handler using memoized helper functions
  const handlePayment = useCallback(() => {
    if (invoiceItems.length === 0) return;

    // Create optimized order update map
    const orderUpdates = createOrderUpdateMap(invoiceItems, orders);

    // Apply all updates and check if orders should be marked as fully paid
    orderUpdates.forEach((updatedOrder, orderId) => {
      // Check if this order is now fully paid
      const isFullyPaid = isOrderFullyPaid(updatedOrder);
      if (isFullyPaid) {
        updatedOrder.payed = true;
      }

      updateOrder(orderId, updatedOrder);
    });

    // Navigate back to table selection
    navigate("/app/kassierer");
  }, [invoiceItems, orders, updateOrder, navigate]);

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-gray-50 dark:bg-gray-900 dark:text-white">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Rechnung - {table?.name || `Tisch ${tableId}`}
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Wählen Sie die Artikel aus, die bezahlt werden sollen
              </p>
            </div>
            <button
              onClick={() => navigate(`/app/kassierer/${tableId}/order`)}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              Zurück zur Bestellung
            </button>
          </div>
        </div>

        {/* Main Content - Two Column Layout */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Column - All Unpaid Items */}
          <div className="flex-1 flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                Offene Artikel ({unpaidItems.length})
              </h2>
            </div>

            <div className="flex-1 overflow-y-auto p-4 space-y-2">
              {unpaidItems.map((item) => (
                <div
                  key={item.id}
                  onClick={() => handleItemClick(item)}
                  className={`
                    p-3 rounded-lg border cursor-pointer transition-all duration-200
                    ${
                      selectedItems.has(item.id)
                        ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400"
                        : "border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"
                    }
                  `}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {item.product.name}
                      </h3>
                      {item.note && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 italic mt-1">
                          {item.note}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {PriceCentUtils.toMoneyString(item.product.priceCents)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}

              {unpaidItems.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">
                    Keine offenen Artikel für diesen Tisch
                  </p>
                </div>
              )}
            </div>

            <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0">
              <button
                onClick={handleReset}
                className="w-full px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                Zurücksetzen
              </button>
            </div>
          </div>

          {/* Right Column - Selected Items (Invoice) */}
          <div className="flex-1 flex flex-col bg-gray-50 dark:bg-gray-900">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                Rechnung ({invoiceItems.length})
              </h2>
            </div>

            <div className="flex-1 overflow-y-auto p-4 space-y-2">
              {invoiceItems.map((item) => (
                <div
                  key={item.id}
                  className="p-3 rounded-lg border border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20"
                >
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {item.product.name}
                      </h3>
                      {item.note && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 italic mt-1">
                          {item.note}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {PriceCentUtils.toMoneyString(item.product.priceCents)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}

              {invoiceItems.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">
                    Keine Artikel ausgewählt
                  </p>
                </div>
              )}
            </div>

            {/* Total and Payment Button */}
            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0">
              <div className="mb-4">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <span className="text-gray-900 dark:text-white">Gesamt:</span>
                  <span className="text-gray-900 dark:text-white">
                    {PriceCentUtils.toMoneyString(invoiceTotal)}
                  </span>
                </div>
              </div>

              <button
                onClick={handlePayment}
                disabled={invoiceItems.length === 0}
                className="w-full px-4 py-3 text-base font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-md transition-colors duration-200"
              >
                Bezahlt
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default InvoiceView;
