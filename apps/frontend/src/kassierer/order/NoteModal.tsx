import React, { useState, useEffect } from 'react';
import type { Product } from '@kassierer/shared/model';

interface NoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddWithNote: (product: Product, note: string) => void;
  product: Product | null;
}

export const NoteModal: React.FC<NoteModalProps> = ({
  isOpen,
  onClose,
  onAddWithNote,
  product,
}) => {
  const [note, setNote] = useState('');

  useEffect(() => {
    if (isOpen) {
      setNote('');
    }
  }, [isOpen]);

  const handleSubmit = () => {
    if (product && note.trim()) {
      onAddWithNote(product, note.trim());
      onClose();
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    } else if (e.key === 'Escape') {
      onClose();
    }
  };

  if (!isOpen || !product) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw]">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Notiz hinzufügen
        </h2>
        
        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
            Produkt: <span className="font-medium">{product.name}</span>
          </p>
        </div>

        <div className="mb-6">
          <label htmlFor="note-input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Notiz:
          </label>
          <textarea
            id="note-input"
            value={note}
            onChange={(e) => setNote(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Notiz eingeben..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white resize-none"
            rows={3}
            autoFocus
          />
        </div>

        <div className="flex justify-between">
          <button
            onClick={handleSubmit}
            disabled={!note.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Hinzufügen
          </button>
          
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Abbrechen
          </button>
        </div>
      </div>
    </div>
  );
};
