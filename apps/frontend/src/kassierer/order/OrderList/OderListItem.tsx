import React from "react";
import { motion } from "framer-motion";
import type { Product } from "@kassierer/shared/model";

type OrderListItemProps = {
  product: Product;
  quantity: number;
  note?: string;
  onIncrease: (product: Product) => void;
  onDecrease: (product: Product) => void;
  onDelete: (product: Product) => void;
};

const numberFormat = new Intl.NumberFormat(undefined, {
  currency: "EUR",
  style: "currency",
});

export const OrderListItem: React.FC<OrderListItemProps> = ({
  product,
  quantity,
  note,
  onIncrease,
  onDecrease,
  onDelete: _onDelete,
}) => {
  const cost = (product.priceCents * quantity) / 100;

  return (
    <tr
      className="w-full flex justify-between items-center bg-gray-700 select-none rounded-sm"
      onClick={() => onIncrease(product)}
    >
      <td className="flex items-center pl-2 py-2 rounded-l-md">
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDecrease(product);
          }}
          className="pr-2"
        >
          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-red-500 text-white leading-none">
            -
          </div>
        </button>
        <motion.div
          key={`kassierer-item-${product.id}-quantity-${quantity}`}
          animate={{ scale: 1 }}
          initial={{ scale: 1.4 }}
          className="w-7 text-right mr-4"
        >
          {quantity} x
        </motion.div>
        <div className="pr-4">
          <div>{product.name}</div>
          {note && (
            <div className="text-sm text-gray-400 italic">
              {note}
            </div>
          )}
        </div>
      </td>
      <td className="border-l-4 border-gray-900 h-full flex items-center pr-2">
      <motion.div
        className="w-16 text-right"
        key={`kassierer-item-${product.id}-cost-${cost}`}
        animate={{ scale: 1 }}
        initial={{ scale: 1.2 }}
      >
        {numberFormat.format(cost)}
      </motion.div>
</td>
    </tr>
  );
};
