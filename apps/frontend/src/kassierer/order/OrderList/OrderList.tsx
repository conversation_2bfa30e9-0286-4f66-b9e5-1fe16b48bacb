import { OrderListItem } from "./OderListItem";
import type { Order, Product, OrderItem } from "@kassierer/shared/model";
import React, { useMemo } from "react";

type Props = {
  products: Product[],
  orderContent: Order["content"];
  onIncrease: (product: Product) => void;
  onDecrease: (product: Product) => void;
  onDelete: (product: Product) => void;
  onIncreaseWithNote: (product: Product, note: string) => void;
};

export const KassiererOrderList: React.FC<Props> = ({
  products,
  orderContent,
  onIncrease,
  onDecrease,
  onDelete,
  onIncreaseWithNote,
}) => {

  const productsMap = useMemo(() => products?.reduce<Record<Product["id"], Product>>(
    (pMap, product) => {
      pMap[product.id] = product;
      return pMap;
    },
    {},
  ), [products]);

  // Group items by product and note
  const groupedItems = useMemo(() => {
    const groups: Array<{
      product: Product;
      items: OrderItem[];
      note?: string;
      key: string;
    }> = [];

    orderContent.forEach((orderContentItem) => {
      const product = productsMap[orderContentItem.productId];
      if (!product) return;

      // Group items by note
      const itemsByNote = new Map<string, OrderItem[]>();

      orderContentItem.items.forEach((item) => {
        const noteKey = item.note || '';
        if (!itemsByNote.has(noteKey)) {
          itemsByNote.set(noteKey, []);
        }
        itemsByNote.get(noteKey)!.push(item);
      });

      // Create separate groups for each note variant
      itemsByNote.forEach((items, noteKey) => {
        groups.push({
          product,
          items,
          note: noteKey || undefined,
          key: `${product.id}-${noteKey}`,
        });
      });
    });

    return groups;
  }, [orderContent, productsMap]);

  return (
    <table className="flex flex-col gap-1 md:pt-0 p-2 overflow-y-auto overflow-x-hidden h-full">
      {groupedItems.map((group) => {
        return (
          <OrderListItem
            key={`kassierer-item-${group.key}`}
            product={group.product}
            quantity={group.items.length}
            note={group.note}
            onIncrease={group.note ? (product) => onIncreaseWithNote(product, group.note!) : onIncrease}
            onDecrease={onDecrease}
            onDelete={onDelete}
          />
        );
      })}
    </table>
  );
};
