import { useProductCategories } from "@/data/useProductCategories";
import { useProducts } from "@/data/useProducts";
import type { Order, Product } from "@kassierer/shared/model";
import { useEffect, useState } from "react";
import layout from "@/kassierer/order/kassierer-layout.module.css";
import { useUserSettings } from "@/data/useUserSettings";
import { OrderSidebar } from "@/kassierer/order/OrderSidebar";
import { KassiererOrderList } from "@/kassierer/order/OrderList/OrderList";
import { KassiererProductList } from "@/kassierer/order/ProductList/ProductList";
import { useOrders } from "@/data/useOrders";
import { useParams, useNavigate } from "react-router-dom";
import { NoteModal } from "@/kassierer/order/NoteModal";

function OrderView() {
  const navigate = useNavigate();

  // Settings
  const darkMode = useUserSettings(state => state.darkMode);
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Products
  const products = useProducts((state) => state.products);
  const categories = useProductCategories((state) => state.categories);

  const {tableId} = useParams();

  // Tables
  /*
  const { observe: observerTables, getAll: getAllTables } = Tables(doc);
  const [tables, setTables] = useState<Table[]>(Array.from(getAllTables()));
  observerTables(() => setTables(Array.from(getAllTables())));
  */

  // const [selectedTableId, setSelectedTableId] = useState("");

  // Order
  const createOrder = useOrders((state) => state.createOrder);

  const [orderId] = useState(crypto.randomUUID());
  const [order, setOrder] = useState<Order>({
    id: orderId,
    content: [],
    createdAt: "",
    tableId: tableId || "",
    userId: 1,
    payed: false
  });

  // Note modal state
  const [isNoteModalOpen, setIsNoteModalOpen] = useState(false);
  const [selectedProductForNote, setSelectedProductForNote] = useState<Product | null>(null);

  const onCreateOrder = () => {
    createOrder(order);
  };

  const onCreateOrderAndInvoice = () => {
    createOrder(order);
    navigate(`/app/kassierer/${tableId}/invoice`);
  };

  const increaseItem = (product: Product) => {
    const existingItem = order.content.find(
      (item) => item.productId === product.id,
    );

    // if it's a completely new one
    if (!existingItem) {
      setOrder({
        ...order,
        content: [
          ...order.content,
          {
            productId: product.id,
            items: [{ payed: false, productId: product.id }],
          },
        ],
      });
      return;
    }

    setOrder({
      ...order,
      content: order.content.map((i) =>
        i.productId === product.id
          ? {
              ...i,
              items: [...i.items, { productId: product.id, payed: false }],
            }
          : i,
      ),
    });
  };

  const decreaseItem = (product: Product) => {
    const orderItems = order.content.find((oc) => oc.productId === product.id);

    // if it will be zero after the decrease
    if (orderItems?.items.length === 1) {
      removeItem(product);
      return;
    }

    setOrder({
      ...order,
      content: order.content.map((oc) =>
        oc.productId === product.id
          ? { ...oc, items: oc.items.slice(0, -1) }
          : oc,
      ),
    });
  };

  const removeItem = (product: Product) => {
    setOrder({
      ...order,
      content: order.content.filter((oc) => oc.productId !== product.id),
    });
  };

  const handleLongPress = (product: Product) => {
    setSelectedProductForNote(product);
    setIsNoteModalOpen(true);
  };

  const handleAddWithNote = (product: Product, note: string) => {
    const existingItem = order.content.find(
      (item) => item.productId === product.id,
    );

    // if it's a completely new product
    if (!existingItem) {
      setOrder({
        ...order,
        content: [
          ...order.content,
          {
            productId: product.id,
            items: [{ payed: false, productId: product.id, note }],
          },
        ],
      });
      return;
    }

    // Add new item with note to existing product
    setOrder({
      ...order,
      content: order.content.map((i) =>
        i.productId === product.id
          ? {
              ...i,
              items: [...i.items, { productId: product.id, payed: false, note }],
            }
          : i,
      ),
    });
  };

  const handleCloseNoteModal = () => {
    setIsNoteModalOpen(false);
    setSelectedProductForNote(null);
  };

  const increaseItemWithNote = (product: Product, note: string) => {
    const existingItem = order.content.find(
      (item) => item.productId === product.id,
    );

    // if it's a completely new product
    if (!existingItem) {
      setOrder({
        ...order,
        content: [
          ...order.content,
          {
            productId: product.id,
            items: [{ payed: false, productId: product.id, note }],
          },
        ],
      });
      return;
    }

    // Add new item with note to existing product
    setOrder({
      ...order,
      content: order.content.map((i) =>
        i.productId === product.id
          ? {
              ...i,
              items: [...i.items, { productId: product.id, payed: false, note }],
            }
          : i,
      ),
    });
  };

  return (
    <div
      className={
        "fixed top-0 left-0 w-full h-full dark:bg-gray-900 dark:text-white dark " +
        layout.wrapper
      }
    >
      <div className={layout.window}>
        <div className={layout.area_sidebar}>
          <OrderSidebar></OrderSidebar>
        </div>
        <div className={layout.area_upper}>
          <KassiererOrderList
            products={products}
            orderContent={order.content}
            onIncrease={increaseItem}
            onDecrease={decreaseItem}
            onDelete={removeItem}
            onIncreaseWithNote={increaseItemWithNote}
          />
        </div>
        <div className={layout.area_lower}>
          <KassiererProductList
            products={products}
            categories={categories}
            onIncrease={increaseItem}
            onLongPress={handleLongPress}
          />
        </div>
        <div className={layout.area_bottom}>
          <div className="flex gap-2 w-full">
            <button
              onClick={onCreateOrder}
              className="flex-1 px-4 py-3 text-base font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors duration-200"
            >
              Bestellen
            </button>
            <button
              onClick={onCreateOrderAndInvoice}
              className="flex-1 px-4 py-3 text-base font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors duration-200"
            >
              Abrechnen
            </button>
          </div>
        </div>
      </div>

      <NoteModal
        isOpen={isNoteModalOpen}
        onClose={handleCloseNoteModal}
        onAddWithNote={handleAddWithNote}
        product={selectedProductForNote}
      />
    </div>
  );
}

export default OrderView;
