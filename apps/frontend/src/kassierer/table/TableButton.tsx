import type { Table } from "@kassierer/shared/model";
import { useLongPress } from "@/hooks/useLongPress";

interface TableButtonProps {
  table: Table;
  hasOpenOrders: boolean;
  onTableClick: (table: Table) => void;
  onTableLongPress: (table: Table) => void;
}

function TableButton({ table, hasOpenOrders, onTableClick, onTableLongPress }: TableButtonProps) {
  const longPressEvents = useLongPress({
    onLongPress: () => onTableLongPress(table),
    onClick: () => onTableClick(table),
    threshold: 500,
  });

  return (
    <button
      {...longPressEvents}
      className={`
        relative rounded-lg border-2 px-3 py-4 text-center font-semibold transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-900 min-h-[4rem]
        ${hasOpenOrders
          ? 'border-red-500 bg-red-50 text-red-700 hover:bg-red-100 dark:border-red-400 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30'
          : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700'
        }
      `}
    >
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-sm sm:text-base font-bold leading-tight">
          {table.name}
        </div>
      </div>
    </button>
  );
}

export default TableButton;
