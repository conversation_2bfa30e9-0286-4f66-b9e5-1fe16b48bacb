import { useTables } from "@/data/useTables";
import { useOrders } from "@/data/useOrders";
import { useUserSettings } from "@/data/useUserSettings";
import { useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import type { Table, Order } from "@kassierer/shared/model";
import TableButton from "./TableButton";

function KassiererTables() {
  const navigate = useNavigate();

  // Settings
  const darkMode = useUserSettings(state => state.darkMode);
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Data
  const tables = useTables((state) => state.tables);
  const orders = useOrders((state) => state.orders);

  // Calculate which tables have open (unpaid) orders
  const tablesWithOpenOrders = useMemo(() => {
    const tableIds = new Set<string>();

    orders.forEach((order: Order) => {
      // Check if this order has any unpaid items
      const hasUnpaidItems = order.content.some(contentItem =>
        contentItem.items.some(item => !item.payed)
      );

      if (hasUnpaidItems && order.tableId) {
        tableIds.add(order.tableId);
      }
    });

    return tableIds;
  }, [orders]);

  const handleTableClick = (table: Table) => {
    navigate(`/app/kassierer/${table.id}/order`);
  };

  const handleTableLongPress = (table: Table) => {
    navigate(`/app/kassierer/${table.id}/invoice`);
  };

  return (
    <div className="fixed top-0 left-0 w-full h-full dark:bg-gray-900 dark:text-white bg-gray-50">
      <div className="h-full overflow-auto">
        <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Tisch auswählen
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Wählen Sie einen Tisch aus, um eine Bestellung aufzunehmen.
            </p>
          </div>

          {/* Tables Grid */}
          <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-col-end-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 gap-3 sm:gap-4">
            {tables.map((table) => {
              const hasOpenOrders = tablesWithOpenOrders.has(table.id);

              return (
                <TableButton
                  key={table.id}
                  table={table}
                  hasOpenOrders={hasOpenOrders}
                  onTableClick={handleTableClick}
                  onTableLongPress={handleTableLongPress}
                />
              );
            })}
          </div>

          {/* Empty State */}
          {tables.length === 0 && (
            <div className="text-center py-16">
              <div className="text-gray-400 dark:text-gray-500">
                <svg className="mx-auto h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Keine Tische verfügbar
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Es wurden noch keine Tische erstellt. Erstellen Sie Tische im Admin-Bereich.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default KassiererTables;
