import { createBrowserRouter, Outlet } from "react-router-dom";
import AdminProductCategories from "./admin/Categories/AdminCategoriesPage.tsx";
import AdminProducts from "./admin/Products/Page";
import AdminPrintersPage from "./admin/Printers/Page";
import AdminLayout from "./admin/layout";
import Kassierer from "./kassierer/kassierer";
import AdminOrderDetail from "./admin/order-detail";
import AdminTablesPage from "./admin/Tables/Page";
import AdminOrderPage from "@/admin/Order/Page";
import AdminClientsPage from "@/admin/clients/AdminClientsPage";
import ClientLoginPage from "@/client/ClientLoginPage";
import LoginPage from "@/user/LoginPage";
import RootPage from "@/RootPage";
import { AuthGuard } from "@/user/AuthGuard";
import { ClientGuard } from "@/client/ClientGuard";
import RegisterPage from "./user/RegisterPage.tsx";
import KassiererTables from "./kassierer/table/table.tsx";
import InvoiceView from "./kassierer/invoice/InvoiceView.tsx";

export const router = createBrowserRouter(
  [
    {
      path: "/",
      element: <RootPage />,
    },
    {
      path: "login",
      element: <LoginPage />,
    },
    {
      id: "register",
      path: "register",
      element: <RegisterPage />,
    },
    {
      path: "client-login",
      element: <ClientLoginPage />,
    },
    {
      path: "app",
      element: <Outlet />,
      children: [
        {
          path: "kassierer",
          element: (
            <ClientGuard>
              <Outlet />
            </ClientGuard>
          ),
          children: [
            {
              index: true,
              element: <KassiererTables />,
            },
            {
              path: ":tableId/order",
              element: <Kassierer />,
            },
            {
              path: ":tableId/invoice",
              element: <InvoiceView />,
            },
          ],
        },
      ],
    },

    {
      path: "admin",
      element: (
        <AuthGuard>
          <AdminLayout />
        </AuthGuard>
      ),
      children: [
        {
          path: "tables",
          element: <AdminTablesPage />,
        },
        {
          path: "product-categories",
          element: <AdminProductCategories />,
        },
        {
          path: "products",
          element: <AdminProducts />,
        },
        {
          path: "printers",
          element: <AdminPrintersPage />,
        },
        {
          path: "orders",
          element: <AdminOrderPage />,
        },
        {
          path: "orders/:orderId",
          element: <AdminOrderDetail />,
        },
        {
          path: "clients",
          element: <AdminClientsPage />,
        },
      ],
    },
  ],
  {
    basename: import.meta.env["VITE_FRONTEND_BASE_PATH"],
  },
);
