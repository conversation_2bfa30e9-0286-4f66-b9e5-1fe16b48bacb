import { create } from "zustand";
import type { PublicUser } from "@kassierer/shared/auth";
import { UserService } from "@/user/user.service";
import { ClientService } from "@/admin/clients/client.service";
import { setGlobalAuthToken } from "@/utils/axios";

interface UserState {
  // State
  token: string | undefined;
  user: PublicUser | undefined;
  isAuthPending: boolean;
  loginError: boolean;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  clientLogin: (token: string) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => void;
  setToken: (token: string | undefined) => void;
  setUser: (user: PublicUser | undefined) => void;
  setAuthPending: (pending: boolean) => void;
  setLoginError: (error: boolean) => void;
  initialize: () => void;
}

// Initialize token from URL or localStorage
const { searchParams } = new URL(window.location.href);
const urlToken = searchParams.get("client_token");
const localToken = localStorage.getItem("access_token");

if (urlToken && !localToken) {
  localStorage.setItem("access_token", urlToken);
}

const initialToken = urlToken ?? localToken ?? undefined;

export const useUserStore = create<UserState>((set, get) => ({
  // Initial state
  token: initialToken,
  user: undefined,
  isAuthPending: true,
  loginError: false,
  
  // Actions
  login: async (email: string, password: string) => {
    try {
      set({ isAuthPending: true, loginError: false });
      const response = await UserService.login(email, password);
      const token = response.data.access_token;
      
      setGlobalAuthToken(token);
      localStorage.setItem("access_token", token);
      set({ token });
      
      // Fetch user data
      const user = await UserService.getUser();
      set({ user, isAuthPending: false });
    } catch {
      set({ loginError: true, isAuthPending: false });
    }
  },

  clientLogin: async (token: string) => {
    try {
      set({ isAuthPending: true, loginError: false });
      const response = await ClientService.clientLogin(token);
      const accessToken = response.access_token;

      setGlobalAuthToken(accessToken);
      localStorage.setItem("access_token", accessToken);
      set({ token: accessToken });

      // For clients, we don't fetch user data (they don't have email/profile)
      set({ user: undefined, isAuthPending: false });
    } catch {
      set({ loginError: true, isAuthPending: false });
      throw new Error("Client login failed");
    }
  },

  register: async (email: string, password: string) => {
    try {
      set({ isAuthPending: true, loginError: false });
      const response = await UserService.register(email, password);
      const token = response.data.access_token;

      setGlobalAuthToken(token);
      localStorage.setItem("access_token", token);
      set({ token });

      // Fetch user data
      const user = await UserService.getUser();
      set({ user, isAuthPending: false });
    } catch {
      set({ loginError: true, isAuthPending: false });
    }
  },

  logout: () => {
    setGlobalAuthToken("");
    localStorage.removeItem("access_token");
    set({
      token: undefined,
      user: undefined,
      isAuthPending: false,
      loginError: false
    });
  },
  
  setToken: (token: string | undefined) => {
    set({ token });
  },
  
  setUser: (user: PublicUser | undefined) => {
    set({ user });
  },
  
  setAuthPending: (isAuthPending: boolean) => {
    set({ isAuthPending });
  },
  
  setLoginError: (loginError: boolean) => {
    set({ loginError });
  },
  
  initialize: async () => {
    const { token } = get();
    
    if (token) {
      setGlobalAuthToken(token);
      try {
        const user = await UserService.getUser();
        set({ user, isAuthPending: false });
      } catch {
        // Token might be invalid, clear it
        get().logout();
      }
    } else {
      set({ isAuthPending: false });
    }
  },
}));

// Initialize the store on module load
useUserStore.getState().initialize();
