import { useUserStore } from "@/stores/userStore";
import { type PropsWithChildren, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export const AuthGuard: React.FC<PropsWithChildren> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isClient = useUserStore((state) => !state.user?.email);
  const isAuthPending = useUserStore((state) => state.isAuthPending);
  const user = useUserStore((state) => state.user);
  const token = useUserStore((state) => state.token);

  useEffect(() => {
    if (!isAuthPending) {
      if (!token) {
        // No authentication, redirect to login
        navigate(`/login?navigate=${location.pathname}`);
      } else if (isClient) {
        // Client trying to access admin area, redirect to kassierer
        navigate("/kassierer");
      } else if (!user) {
        // Token exists but no user data (shouldn't happen for regular users)
        navigate(`/login?navigate=${location.pathname}`);
      }
    }
  }, [isAuthPending, user, token, isClient, navigate, location.pathname]);

  if (isAuthPending) {
    return (
      <div className="flex min-h-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Laden...</p>
        </div>
      </div>
    );
  }

  if (!token || !user || isClient) {
    return (
      <div className="flex min-h-full items-center justify-center">
        <div className="text-center">
          <p className="text-sm text-gray-600">Nicht autorisiert</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
