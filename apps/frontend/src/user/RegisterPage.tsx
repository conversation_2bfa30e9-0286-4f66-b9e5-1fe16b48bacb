import { ErrorMessage, Field, Form, Formik, type FormikHelpers } from "formik";
import { useNavigate, useSearchParams } from "react-router-dom";

import { useEffect } from "react";
import { useUserStore } from "@/stores/userStore";

type FormValues = {
  email: string;
  password: string;
};

export default function RegisterPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { register, token, isAuthPending } = useUserStore();

  const navigateToPath = searchParams.get("navigate");

  useEffect(() => {
    if (!isAuthPending && token) {
      navigate(navigateToPath?.length ? navigateToPath : "/admin/products");
    }
  }, [token, isAuthPending]);

  return (
    <>
      <div className="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <h2 className="mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900">
            <PERSON><PERSON><PERSON> einen Account
          </h2>
        </div>

        <div className="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
          <Formik
            initialValues={
              {
                email: "",
                password: "",
              } as FormValues
            }
            onSubmit={(
              values: FormValues,
              actions: FormikHelpers<FormValues>,
            ) => {
              register(values.email, values.password)
                .then(() => {
                  navigate(
                    navigateToPath?.length
                      ? navigateToPath
                      : "/admin/products",
                  );
                })
                .catch(() => {
                  actions.setErrors({
                    email: "Der Login war nicht erfolgreich",
                  });
                })
                .finally(() => {
                  actions.setSubmitting(false);
                });
            }}
          >
            <Form className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium leading-6 text-gray-900"
                >
                  Email-Adresse
                </label>
                <div className="mt-2">
                  <ErrorMessage name="email" />
                  <Field
                    id="email"
                    name="email"
                    type="email"
                    required
                    tabIndex="1"
                    autoComplete="current-user"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  />
                </div>
              </div>
              <div>
                <div className="flex items-center justify-between">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium leading-6 text-gray-900"
                  >
                    Passwort
                  </label>
                  <div className="text-sm">
                    <a
                      href="#"
                      className="font-semibold text-indigo-600 hover:text-indigo-500"
                    >
                      Passwort vergessen?
                    </a>
                  </div>
                </div>
                <div className="mt-2">
                  <Field
                    id="password"
                    name="password"
                    type="password"
                    required
                    tabIndex="2"
                    autoComplete="current-password"
                    className="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-xs ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  />
                </div>
              </div>
              <div>
                <button
                  type="submit"
                  className="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-solid focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  Einloggen
                </button>
              </div>
            </Form>
          </Formik>
          <p className="mt-10 text-center text-sm text-gray-500">
            Noch kein Benutzer?{" "}
            <a
              href="#"
              className="font-semibold leading-6 text-indigo-600 hover:text-indigo-500"
            >
              Erstelle einen Account
            </a>
          </p>
        </div>
      </div>
    </>
  );
}
