export type ClientInfo = {
  id: string;
  userId: string;
  isActive: boolean;
  createdAt: string;
  lastActiveAt?: string;
  name?: string;
};

export type TemporaryTokenInfo = {
  token: string;
  expiresAt: string;
};

export type ClientLoginRequest = {
  token: string;
};

export type ClientLoginResponse = {
  access_token: string;
};

export type GenerateTokenResponse = {
  token: string;
  expiresAt: string;
};
